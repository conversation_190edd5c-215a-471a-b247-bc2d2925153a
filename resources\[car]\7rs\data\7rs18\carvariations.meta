<?xml version="1.0" encoding="UTF-8"?>

<CVehicleModelInfoVariation>
    <variationData>
	
        <Item>
            <modelName>7rs18</modelName>
            <colors>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        156
                        156
                    </indices>
                    <liveries>
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        156
                        156
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        156
                        156
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        156
                        156
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
                <Item>
                    <indices content="char_array">
                        0
                        0
                        156
                        156
                    </indices>
                    <liveries>
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="true" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                        <Item value="false" />
                    </liveries>
                </Item>
            </colors>
            <kits>
                <Item>0_default_modkit</Item>
            </kits>
            <windowsWithExposedEdges />
            <plateProbabilities>
                <Probabilities>
                    <Item>
                        <Name>police guv plate</Name>
                        <Value value="100" />
                    </Item>
                </Probabilities>
            </plateProbabilities>
            <lightSettings value="1" />
            <sirenSettings value="5789" />
        </Item>
       
    </variationData>
</CVehicleModelInfoVariation>