local plates = {
	[0] = "plate01",
	[1] = "vehicle_generic_plate_font",
	[2] = "plate02",
	[3] = "plate03",
	[4] = "plate04",
	[5] = "plate05",
	[6] = "yankton_plate"
}
local runtimeTexture = "customPlates"
local defaultNormal = "defaultNormalTexture"
local vehShare = "vehshare"
local plateTxd = CreateRuntimeTxd(runtimeTexture)
CreateRuntimeTextureFromImage(plateTxd, defaultNormal, "plates/plateNormals.png")

for plateIndex, plateName in pairs(plates) do
	local cvarData = GetConvar("plate_override_" .. plateName, false)
	if cvarData then
		local plateOverride = json.decode(cvarData)
		local plateNormal = plateName .. "_n"
		
		if plateOverride.fileName then
			CreateRuntimeTextureFromImage(plateTxd, plateName, plateOverride.fileName)
			AddReplaceTexture(vehShare, plateName, runtimeTexture, plateName)
		end
		if plateOverride.normalName then
			CreateRuntimeTextureFromImage(plateTxd, plateNormal, plateOverride.normalName)
			AddReplaceTexture(vehShare, plateNormal, runtimeTexture, plateNormal)
		else
			AddReplaceTexture(vehShare, plateNormal, runtimeTexture, defaultNormal)
		end
		print(plateIndex, plateOverride.pattern)
		SetDefaultVehicleNumberPlateTextPattern(plateIndex, plateOverride.pattern)
	end
end

-- local runtimeTexture = "customPlatesfont"
-- local defaultNormal = "defaultNormalTexture"
-- local vehShare = "vehshare"
-- local plateTxd = CreateRuntimeTxd(runtimeTexture)
-- CreateRuntimeTextureFromImage(plateTxd, defaultNormal, "plates/vehicle_generic_plate_font_n.png")

-- for plateIndex, plateName in pairs(plates) do
-- 	local cvarData = GetConvar("plate_font_" .. plateName, false)
-- 	if cvarData then
-- 		local platefont = json.decode(cvarData)
-- 		local plateNormal = plateName .. "_n"
		
-- 		if platefont.fileName then
-- 			CreateRuntimeTextureFromImage(plateTxd, plateName, platefont.fileName)
-- 			AddReplaceTexture(vehShare, plateName, runtimeTexture, plateName)
-- 		end
-- 		if platefont.normalName then
-- 			CreateRuntimeTextureFromImage(plateTxd, plateNormal, platefont.normalName)
-- 			AddReplaceTexture(vehShare, plateNormal, runtimeTexture, plateNormal)
-- 		else
-- 			AddReplaceTexture(vehShare, plateNormal, runtimeTexture, defaultNormal)
-- 		end
-- 		SetDefaultVehicleNumberPlateTextPattern(plateIndex, platefont.pattern)
-- 	end
-- end