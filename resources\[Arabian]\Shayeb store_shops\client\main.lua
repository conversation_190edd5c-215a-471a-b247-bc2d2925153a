local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}
local showblip = true
local displayedBlips = {}
local AllBlips = {}
local number = nil
local BLOCKINPUTCONTROL = false
local syncingTimer = 0

local ShopId 		 = nil
local Msg        = nil
local HasAlreadyEnteredMarker = false
local LastZone                = nil

local Cart = {}
local vehPlate = nil

local TimerrrrkNdd = 0

local Var = nil
local Coordss = nil
local OnRobbery = false
local Id = nil
local Name = nil
--Mazad
local ShopLabell = {
	['market'] = '<font color=0059BE>متجر</font>',
	['bar'] = '<font color=7C00BE>بار</font>',
	['pharmacie'] = '<font color=F53030>صيدلية</font>',
	['rts'] = '<font color=f9a825>المطاعم</font>',
	['weapons'] = '<font color=EC7710>محل أسلحة</font>',
	['SodaMachine'] = '<font color=ECA71B>براد</font>',
}
local ShopLabell2 = {
	['market'] = 'متجر',
	['bar'] = 'بار',
	['pharmacie'] = 'صيدلية',
	['rts'] = 'مطعم',
	['weapons'] = 'محل أسلحة',
	['SodaMachine'] = 'براد',
}

local ShopLabell3 = {
	['market'] = 'ﺮﺠﺘﻣ',
	['bar'] = 'ﺭﺎﺑ',
	['pharmacie'] = 'ﺔﻴﻟﺪﻴﺻ',
	['rts'] = 'ﻢﻌﻄﻣ',
	['weapons'] = 'محل أسلحة',
	['SodaMachine'] = 'ﺩﺍﺮﺑ',
}

local blipRobbery = nil

function getshoptype(ndndndndndnnd)
	for k,v in pairs(Config.Zones) do
		if ndndndndndnnd == 2000 then
			return truck
		elseif v.Pos.number == ndndndndndnnd then
			return v.Type
		end
	end
end

configready = false
Config = nil

RegisterNetEvent('esx_shops2:updateconfig')
AddEventHandler('esx_shops2:updateconfig', function(data)
	Config = data
	Wait(1000)
	configready = true
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESX.PlayerData = xPlayer
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESX.PlayerData.job = job
end)

AddEventHandler('playerSpawned', function()
	Citizen.Wait(5000)

	TriggerServerEvent('esx_shops2:spawned', securityToken)
end)

AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
		SetNuiFocus(false, false)
	end
end)

RegisterNUICallback('escape', function(data, cb)

	SetNuiFocus(false, false)

	SendNUIMessage({
		type = "close",
	})
	BLOCKINPUTCONTROL = false

	Wait(1000)
	ShopId = number
end)

RegisterNUICallback('bossactions', function(data, cb)

	SetNuiFocus(false, false)

	SendNUIMessage({
		type = "close",
	})
	BLOCKINPUTCONTROL = false

	ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data)
		exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
		Citizen.SetTimeout(1000, function()
			if data.owneroremps then
				OpenBoss(data.owner)
			else
				ESX.ShowNotification('<font color=red>إدارة المتجر متاحة لصاحب المتجر والموظفين فقط</font>')
			end
		end)
	end)
end)

RegisterNUICallback('putcart', function(data, cb)
	table.insert(Cart, {item = data.item, label = data.label, count = data.count, id = data.id, price = data.price})
	cb(Cart)
end)

RegisterNUICallback('notify', function(data, cb)
	ESX.ShowNotification(data.msg)
end)

RegisterNUICallback('refresh', function(data, cb)

	Cart = {}

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
		ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

			if data ~= nil then
				Owner = true
			end

			if result ~= nil then
				local type2 = getshoptype(number)

				SetNuiFocus(true, true)
				BLOCKINPUTCONTROL = true
				SendNUIMessage({
					type = "shop",
					type2 = type2,
					result = result,
					ShopName = data[1].ShopName,
					owner = Owner,
				})
			end

		end, number)
	end, number)
end)

RegisterNUICallback('emptycart', function(data, cb)
	Cart = {}

end)

RegisterNUICallback('buy', function(data, cb)
	local type = getshoptype(number)
	if type then
		for i=1, #Config.Items[type], 1 do
			if Config.Items[type][i].itemConvert == data.Item then
				if Config.Items[type][i].info then
					if exports.napoly_xplevel.ESXP_GetRank() >= Config.Items[type][i].info.xp then
						if Config.Items[type][i].info.lisence then
							ESX.TriggerServerCallback('esx_license:checkLicense', function(hasLicense)
								if hasLicense then
									if vehPlate then
										TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken,vehPlate)

									else
										TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
									end
								else
									ESX.ShowNotification('<font color=red>أنت لا تملك رخصة سلاح</font>')
								end
							end, GetPlayerServerId(PlayerId()), Config.Items[type][i].info.lisence)
						end
					else
						ESX.ShowNotification('<font color=red>الخبرة المطلوبة للسلعة </font><font color=orange>'..tostring(Config.Items[type][i].info.xp))
					end
				else
					if vehPlate then
						TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken,vehPlate)

					else
						TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
					end
				end
			end
		end
	else
		for i=1, #Config.Items["truck"], 1 do
			if Config.Items["truck"][i].itemConvert == data.Item then
				if Config.Items["truck"][i].info then
					if exports.napoly_xplevel.ESXP_GetRank() >= Config.Items["truck"][i].info.xp then
						if Config.Items["truck"][i].info.lisence then
							ESX.TriggerServerCallback('esx_license:checkLicense', function(hasLicense)
								if hasLicense then
									if vehPlate then
										TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken,vehPlate)

									else
										TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
									end
								else
									ESX.ShowNotification('<font color=red>أنت لا تملك رخصة سلاح</font>')
								end
							end, GetPlayerServerId(PlayerId()), Config.Items["truck"][i].info.lisence)
						end
					else
						ESX.ShowNotification('<font color=red>الخبرة المطلوبة للسلعة </font><font color=orange>'..tostring(Config.Items["truck"][i].info.xp))
					end
				else
					if vehPlate then
						TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken,vehPlate)

					else
						TriggerServerEvent('esx_kr_shops:Buy', number, data.Item, data.Count, securityToken)
					end
				end
			end
		end
	end
	Cart = {}
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(835.69, -3193.38, 14.5)

	SetBlipSprite (blip, 473)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='A9eelsh'>ﻲﺴﻴﺋﺮﻟﺍ ﻉﺩﻮﺘﺴﻤﻟﺍ")
	EndTextCommandSetBlipName(blip)
	-- Citizen.CreateThread(function()
	-- 	while true do
	-- 	  Citizen.Wait(1)
	-- 		if not IsBlipFlashing(blip) then
	-- 		  SetBlipScale(blip, 1.1 + math.sin(GetGameTimer() / 200) * 0.4)
	-- 	 	end
	-- 	end
	--   end)
end)

Citizen.CreateThread(function()
	Citizen.Wait(500)
	local blip = AddBlipForCoord(-120.18, -2505.69, 14.64)

	SetBlipSprite (blip, 473)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 2)
	SetBlipAsShortRange(blip, true)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString("<FONT FACE='A9eelsh'>ﻲﺑﺮﻐﻟﺍ ﻉﺩﻮﺘﺴﻤﻟﺍ")
	EndTextCommandSetBlipName(blip)
end)

if Hamada.DisableBankBlip == nil then

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(232.94, 216.1, 157.62)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 59)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>ﻱﺰﻛﺮﻤﻟﺍ ﻚﻨﺒﻟﺍ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(148.98, -1039.98, 49.22)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>ﺪﻴﻬﺸﻟﺍ ﺔﻘﻳﺪﺣ ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(-1215.71, -333.07, 42.12)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>( 1 ) ﻲﻋﺮﻔﻟﺍ  ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(-349.17, -46.24, 90.91)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>( 2 ) ﻲﻋﺮﻔﻟﺍ  ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(314.46, -277.59, 91.08)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>( 3 ) ﻲﻋﺮﻔﻟﺍ  ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(-2959.27, 481.13, 25.67)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>( 1 ) ﻲﻟﺎﻤﺸﻟﺍ  ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(-109.23, 6464.49, 37.22)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.80)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>ﻲﺴﻴﺋﺮﻟﺍ ﻮﺘﻴﻟﻮﺑ ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(1174.66, 2711.8, 42.85)

		SetBlipSprite (blip, 536)
		SetBlipDisplay(blip, 4)
		SetBlipScale  (blip, 0.85)
		SetBlipColour (blip, 2)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>ﻲﺑﻮﻨﺠﻟﺍ ﻱﺪﻧﺎﺳ ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)

	Citizen.CreateThread(function()
		Citizen.Wait(500)
		local blip = AddBlipForCoord(5.23, -707.73, 222.73)

		SetBlipSprite (blip, 269)
		SetBlipDisplay(blip, 0)
		SetBlipScale  (blip, 1.30)
		SetBlipColour (blip, 70)
		SetBlipAsShortRange(blip, true)
		BeginTextCommandSetBlipName("STRING")
		AddTextComponentString("<FONT FACE='A9eelsh'>ﻲﻤﻟﺎﻌﻟﺍ  ﻚﻨﺑ")
		EndTextCommandSetBlipName(blip)
	end)
end
AddEventHandler('esx_kr_shop:hasEnteredMarker', function(zone,plate)
	if zone == 'center' then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open_center')
	elseif zone == 'crafting' then
		ShopId = zone
		Msg  = '<font face="A9eelsh">ﺡﻼﺴﻟﺍ ﻊﻴﻨﺼﺘﻟ ~y~E ~w~ﻂﻐﺿﺇ'
	elseif zone == 2000 and plate then
		ShopId = zone
		number = zone
		vehPlate = plate
		Msg  = _U('press_to_open')
	elseif zone <= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_open')
	elseif zone >= 100 then
		ShopId = zone
		number = zone
		Msg  = _U('press_to_rob')
	end
end)

AddEventHandler('esx_kr_shop:hasExitedMarker', function(zone)
	ShopId = nil
	ESX.UI.Menu.CloseAll()
end)

Citizen.CreateThread(function ()
	while not configready do Citizen.Wait(1000) end
	while true do
		Wait(1)
		local sleep, _this_shop = 1500, ShopId

		if ShopId ~= nil then

			ESX.ShowHelpNotification(Msg)

			sleep = 0
			if IsControlJustReleased(0, Keys['E']) then
				if TimerrrrkNdd == 0 then
					exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
					TimerrrrkNdd = 1
					Citizen.SetTimeout(1000, function()
						TimerrrrkNdd = 0

						if _this_shop == 'center' then
							OpenShopCenter()
						elseif _this_shop == 'crafting' then
							CraftingWeapons()
						elseif _this_shop == 2000 and vehPlate then
							ESX.TriggerServerCallback('esx_kr_shop:getOwnedTrucks', function(data)
								print("Received data from getOwnedTrucks:", data)
								ESX.TriggerServerCallback('esx_kr_shop:getTruckItems', function(result)
									print("Received result from getTruckItems:", result)
									if data ~= nil then
										Owner = true
									end
							
									if result ~= nil then
										local type2 = getshoptype(number)
										SetNuiFocus(true, true)
										BLOCKINPUTCONTROL = true
										SendNUIMessage({
											type = "shop",
											type2 = type2,
											result = result,
											ShopName =  data[1].ShopName,
											owner = Owner,
										})
									end
								end, plate)
							end, plate)
							
						elseif _this_shop <= 100 then
							ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
								ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

									if data ~= nil then
										Owner = true
									end

									if result ~= nil then
										local type2 = getshoptype(number)
										SetNuiFocus(true, true)
										BLOCKINPUTCONTROL = true
										SendNUIMessage({
											type = "shop",
											type2 = type2,
											result = result,
											ShopName =  data[1].ShopName,
											owner = Owner,
										})
									end

								end, number)
							end, number)
						elseif _this_shop >= 100 then
							Robbery(number - 100)
						end
					end)
				end

				ShopId = nil
			elseif IsControlJustReleased(0, Keys['H']) then
				print('_this_shop', _this_shop)
				if _this_shop == 2000 and vehPlate then
					ESX.TriggerServerCallback('esx_kr_shop:getOwnerTrucks', function(data, grade)
						exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
						Citizen.SetTimeout(1000, function()
							if data ~= nil then
								if grade == 3 then
									OpenBoss(true, data.number,vehPlate)
								elseif grade > 0 then
									OpenBoss(false, data.number,vehPlate)
								else
									ESX.ShowNotification('<font color=red>إدارة المتجر متاحة لصاحب المتجر والموظفين فقط</font> 1')
								end
							end
						end)
					end, vehPlate)
				else
					ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data)
						exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
						Citizen.SetTimeout(1000, function()
							if data.owneroremps and number == data.number then
								OpenBoss(data.owner, data.number)
							else
								ESX.ShowNotification('<font color=red>إدارة المتجر متاحة لصاحب المتجر والموظفين فقط</font> 2')
							end
						end)
					end, _this_shop)
				end

				ShopId = nil
			end

			Citizen.Wait(sleep)
		end
	end
end)


function OpenBoss(isowner, number, plate)
	if plate then
		ESX.TriggerServerCallback('esx_kr_shop:getOwnedTrucks', function(data)

			local elements = {}
			local number = number
			table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
			--table.insert(elements, {label = 'Shipments',    value = 'shipments'})
			table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
			if isowner then
				table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
			end
			table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
			if isowner then
				table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
			end
			table.insert(elements, {label = 'تغيير سعر سلعة في المتجر',    value = 'resellitem'})
			if isowner then
				-- table.insert(elements, {label = 'إدارة الموظفين',    value = 'emps'})
				table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
				-- table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})
			end


			local type = getshoptype(number)
			local _this_menu_label = '<font color=gray> إدارة البراد </font>'

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'boss',
			{
				title    = _this_menu_label ..' | <font color=gray>'..data[1].ShopName..'</font>',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				if data.current.value == 'putitem' then
					exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
					Citizen.SetTimeout(1000, function()
						menu.close()
						PutItem(number,vehPlate)
					end)
				elseif data.current.value == 'resellitem' then
					resellitem(number,plate)
				elseif data.current.value == 'takeitem' then
					exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
					Citizen.SetTimeout(1000, function()
						TakeItem(number,vehPlate)
					end)
				elseif data.current.value == 'takemoney' then

					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
						title = 'كم تريد أن تسحب'
					}, function(data2, menu2)

						local amount = tonumber(data2.value)

						TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number, plate)

						menu2.close()

					end,
					function(data2, menu2)
						menu2.close()
					end)

				elseif data.current.value == 'putmoney' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
						title = 'كم تريد أن تودع؟'
					}, function(data3, menu3)
						local amount = tonumber(data3.value)
						TriggerServerEvent('esx_kr_shops:addMoney', amount, number,plate)
						menu3.close()
					end,
					function(data3, menu3)
						menu3.close()
					end)

				elseif data.current.value == 'sell' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
						title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
					}, function(data4, menu4)

						if data4.value == 'نعم' then
							TriggerServerEvent('esx_kr_shops:SellShop', number, vehPlate)
							menu4.close()
						end
					end,
					function(data4, menu4)
						menu4.close()
					end)

				elseif data.current.value == 'changename' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
						title = 'ماذا تريد تسمية متجرك؟'
					}, function(data5, menu5)

						TriggerServerEvent('esx_kr_shops:changeName', number, data5.value,vehPlate)
						menu5.close()
					end,
					function(data5, menu5)
						menu5.close()
					end)

				end
			end,
			function(data, menu)
				menu.close()
				Wait(1000)
				ShopId = number
			end)
		end, plate)
	else
		ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)
			local elements = {}
			local number = number
			table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
			--table.insert(elements, {label = 'Shipments',    value = 'shipments'})
			table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
			if isowner then
				table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
			end
			table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
			if isowner then
				table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
			end
			table.insert(elements, {label = 'تغيير سعر سلعة في المتجر',    value = 'resellitem'})
			if isowner then
				table.insert(elements, {label = 'إدارة الموظفين',    value = 'emps'})
				table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
				table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})
			end

			local type = getshoptype(number)
			local _this_menu_label = '<font color=gray> إدارة المتجر</font>'

			if type == "SodaMachine" then
				_this_menu_label = '<font color=gray> إدارة البراد </font>'
			elseif type == "bar" then
				_this_menu_label = '<font color=gray> إدارة البار</font>'
			elseif type == "pharmacie" then
				_this_menu_label = '<font color=gray> إدارة الصيدلية</font>'
			elseif type == "rts" then
				_this_menu_label = '<font color=gray>إدارة المـطـعـم </font>'
			elseif type == "weapons" then
				_this_menu_label = '<font color=gray>إدارة محل الأسلحة </font>'
			end

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'boss',
			{
				title    = _this_menu_label ..' | <font color=gray>'..data[1].ShopName..'</font>',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				if data.current.value == 'putitem' then
					exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
					Citizen.SetTimeout(1000, function()
						menu.close()
						PutItem(number)
					end)
				elseif data.current.value == 'resellitem' then
					resellitem(number)
				elseif data.current.value == 'emps' then
					ESX.UI.Menu.Open(
					'default', GetCurrentResourceName(), 'emps',
					{
						title    = 'إدارة الموظفين',
						align    = 'bottom-right',
						elements = {
							--{label = '<span style="color:white">عدد الموظفين: </span><span style="color:orange">'..countEmployees..'</span><span style="color:gray">/'..maxEmployees..'</span>'},
							{label = '<font color=green>توظيف</font>', value = 'add_emp'},
							{label = '<font color=red>طرد</font>', value = 'remove_emp'},
						}
					}, function(data2, menu2)
						if data2.current.value == 'add_emp' then
							-------------------------------------------
							-------------------------------------------
							--------------------------------------------
							local playerPed = PlayerPedId()
							local playersNearby = ESX.Game.GetPlayersInArea(GetEntityCoords(playerPed), 3.0)

							if #playersNearby > 0 then
								local players = {}
								elements = {}

								for k,playerNearby in ipairs(playersNearby) do
									players[GetPlayerServerId(playerNearby)] = true
								end

								ESX.TriggerServerCallback('esx:getPlayerNames', function(returnedPlayers)
									for playerId,playerName in pairs(returnedPlayers) do
										table.insert(elements, {
											label = playerName,
											playerId = playerId
										})
									end

									ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'add_emp', {
										title    = 'اختر الشخص المراد توظيفه',
										align    = 'bottom-right',
										elements = elements
									}, function(data2, menu2)
										local selectedPlayer, selectedPlayerId = GetPlayerFromServerId(data2.current.playerId), data2.current.playerId
										playersNearby = ESX.Game.GetPlayersInArea(GetEntityCoords(playerPed), 3.0)
										playersNearby = ESX.Table.Set(playersNearby)

										if playersNearby[selectedPlayer] then
											local selectedPlayerPed = GetPlayerPed(selectedPlayer)

											if IsPedOnFoot(selectedPlayerPed) and not IsPedFalling(selectedPlayerPed) then
												TriggerServerEvent('esx_shops2:setemps', number, selectedPlayerId)
											else
												ESX.ShowNotification('لايمكن توظيف اي شخص داخل المركبة')
											end
										else
											ESX.ShowNotification('<font color=red>لايوجد لاعب قريب منك</font>')
											menu2.close()
										end
									end, function(data2, menu2)
										menu2.close()
									end)
								end, players)
							else
								ESX.ShowNotification('<font color=red>لايوجد لاعب قريب منك</font>')
							end
							-------------------------------------------
							-------------------------------------------
							-------------------------------------------
						elseif data2.current.value == 'remove_emp' then
							ESX.TriggerServerCallback('esx_shops2:getempslist', function(data)
								local elements3 = {}
								for i = 1, #data, 1 do
									if data[i] then
										table.insert(elements3, { label = data[i].firstname.. ' ' ..data[i].lastname, value = data[i].identifier })
									end
								end

								if #elements == 0 then
									table.insert(elements, { label = 'لايوجد موظفون بالمتجر' })
								end

								ESX.UI.Menu.Open('default', GetCurrentResourceName(), 'remove_emp', {
									title    = 'اختر الشخص المراد طرده',
									align    = 'bottom-right',
									elements = elements3
								}, function(data2, menu2)
									TriggerServerEvent('esx_shops2:removeemps', number, data2.current.value)
									ESX.UI.Menu.CloseAll()
								end, function(data2, menu2)
									menu2.close()
								end)
							end, number)
						end
					end, function(data2, menu2)
						menu2.close()
					end)
				elseif data.current.value == 'takeitem' then
					exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
					Citizen.SetTimeout(1000, function()
						TakeItem(number)
					end)
				elseif data.current.value == 'takemoney' then


					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
						title = 'كم تريد أن تسحب'
					}, function(data2, menu2)

						local amount = tonumber(data2.value)

						TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number, securityToken)

						menu2.close()

					end,
					function(data2, menu2)
						menu2.close()
					end)

				elseif data.current.value == 'putmoney' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
						title = 'كم تريد أن تودع؟'
					}, function(data3, menu3)
						local amount = tonumber(data3.value)
						TriggerServerEvent('esx_kr_shops:addMoney', amount, number)
						menu3.close()
					end,
					function(data3, menu3)
						menu3.close()
					end)

				elseif data.current.value == 'sell' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
						title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
					}, function(data4, menu4)

						if data4.value == 'نعم' then
							TriggerServerEvent('esx_kr_shops:SellShop', number)
							menu4.close()
						end
					end,
					function(data4, menu4)
						menu4.close()
					end)

				elseif data.current.value == 'changename' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
						title = 'ماذا تريد تسمية متجرك؟'
					}, function(data5, menu5)

						TriggerServerEvent('esx_kr_shops:changeName', number, data5.value)
						menu5.close()
					end,
					function(data5, menu5)
						menu5.close()
					end)

				end
			end,
			function(data, menu)
				menu.close()
				Wait(1000)
				ShopId = number
			end)
		end, number)
	end
end

function GetAllShipments(id,plate)
	if plate then
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getTime', function(time)
			ESX.TriggerServerCallback('esx_kr_shop:getAllShipments', function(items)

				local once = true
				local once2 = true

				for i=1, #items, 1 do

					if time - items[i].time >= Config.DeliveryTime and once2 then
						table.insert(elements, {label = '<font color=white>-- <font color=green>طلبات جاهزة للتسليم<font color=white> --'})
						once2 = false
					end

					if time - items[i].time >= Config.DeliveryTime then
						table.insert(elements, {label = '<font color=white>'..items[i].label..'<font color=gray>[<font color=orange>'..items[i].count..'<font color=gray>]', value = items[i].item, price = items[i].price, idd = items[i].Command })
					end

					if time - items[i].time <= Config.DeliveryTime and once then
						table.insert(elements, {label = '<font color=white>--<font color=red>طلبات جاري شحنها للمتجر<font color=white>--'})
						once = false
					end

					if time - items[i].time <= Config.DeliveryTime then
						times = time - items[i].time
						table.insert(elements, {label = '<font color=orange>'..items[i].label .. ' - <font color=#1B76F9> الكمية : <font color=white>'..items[i].count .. ' - <font color=#1B76F9> الوقت المتبقي : <font color=white>' .. math.floor((Config.DeliveryTime - times) / 60) .. ' دقيقة' })
					end

				end

				if #elements == 0 then
					table.insert(elements, { label = '<font color=grey>لاتوجد شحنات</font>' })
				end

				ESX.UI.Menu.Open(
				'default', GetCurrentResourceName(), 'allshipments',
				{
					title    = '<font color=#ffffff>المستودع العام - <font color=orange>تتبع شحنات',
					align    = 'bottom-right',
					elements = elements
				},
				function(data, menu)
					if data.current.value then
						ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeamount_shup',
						{
							title = "الكميه"
						},
						function(data2, menu2)

							local takeamount = tonumber(data2.value)

							menu2.close()
							if takeamount == nil then
								ESX.ShowNotification("يجب كتابة عدد صحيح")
							else
								TriggerServerEvent('esx_kr_shops:GetAllItems', id, data.current.value, data.current.idd, takeamount,plate)
								GetAllShipments(id, plate)
							end
							menu.close()
						end,
						function(data2, menu2)
							menu2.close()
						end)
					end
				end, function(data, menu)
					menu.close()
				end)

			end, "2000",plate)
		end)
	else
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getTime', function(time)
			ESX.TriggerServerCallback('esx_kr_shop:getAllShipments', function(items)

				local once = true
				local once2 = true

				for i=1, #items, 1 do

					if time - items[i].time >= Config.DeliveryTime and once2 then
						table.insert(elements, {label = '<font color=white>-- <font color=green>طلبات جاهزة للتسليم<font color=white> --'})
						once2 = false
					end

					if time - items[i].time >= Config.DeliveryTime then
						table.insert(elements, {label = '<font color=white>'..items[i].label..'<font color=gray>[<font color=orange>'..items[i].count..'<font color=gray>]', value = items[i].item, price = items[i].price, idd = items[i].Command })
					end

					if time - items[i].time <= Config.DeliveryTime and once then
						table.insert(elements, {label = '<font color=white>--<font color=red>طلبات جاري شحنها للمتجر<font color=white>--'})
						once = false
					end

					if time - items[i].time <= Config.DeliveryTime then
						times = time - items[i].time
						table.insert(elements, {label = '<font color=orange>'..items[i].label .. ' - <font color=#1B76F9> الكمية : <font color=white>'..items[i].count .. ' - <font color=#1B76F9> الوقت المتبقي : <font color=white>' .. math.floor((Config.DeliveryTime - times) / 60) .. ' دقيقة' })
					end

				end

				if #elements == 0 then
					table.insert(elements, { label = '<font color=grey>لاتوجد شحنات</font>' })
				end

				ESX.UI.Menu.Open(
				'default', GetCurrentResourceName(), 'allshipments',
				{
					title    = '<font color=#ffffff>المستودع العام - <font color=orange>تتبع شحنات',
					align    = 'bottom-right',
					elements = elements
				},
				function(data, menu)
					if data.current.value then
						ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeamount_shup',
						{
							title = "الكميه"
						},
						function(data2, menu2)

							local takeamount = tonumber(data2.value)

							menu2.close()
							if takeamount == nil then
								ESX.ShowNotification("يجب كتابة عدد صحيح")
							else
								TriggerServerEvent('esx_kr_shops:GetAllItems', id, data.current.value, data.current.idd, takeamount)
								GetAllShipments(id)
							end
							menu.close()
						end,
						function(data2, menu2)
							menu2.close()
						end)
					end
				end, function(data, menu)
					menu.close()
				end)

			end, id)
		end)
	end
end

function OpenShipmentDelivery(id,plate)
	if plate then
		ESX.UI.Menu.CloseAll()
		local elements = {}
		local type = "truck"

		for i=1, #Config.Items[type], 1 do
			if Config.Items[type][i].Storge == nil or Config.Items[type][i].Storge ~= false then
				table.insert(elements, {labels =  Config.Items[type][i].label, label =  '<font color=white> '.. Config.Items[type][i].label .. '<font color=red> | <font color=green> $<font color=white>' .. Config.Items[type][i].price..'<font color=red> | <font color=orange>'..Config.Items[type][i].count..'</font><font color=gray> في الصندوق </font>',	value = Config.Items[type][i].item, price = Config.Items[type][i].price})
			end
		end

		ESX.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'shipitem',
		{
			title    = '<font color=#ffffff>المستودع العام - <font color=orange>طلب منتجات جديدة',
			align    = 'bottom-right',
			elements = elements
		},
		function(data, menu)
			menu.close()
			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'krille', {
				title = 'كم تريد شراء؟'
			}, function(data2, menu2)
				menu2.close()
				ESX.TriggerServerCallback('esx_shops2:canorder', function(canorder)
					if (canorder + tonumber(data2.value)) <= Config.BoxMax[type] then
						TriggerServerEvent('esx_kr_shop:MakeShipment', id, data.current.value, data.current.price, tonumber(data2.value), data.current.labels,plate)
					else
						ESX.ShowNotification('<font color=red>لا يمكن طلب أكثر من <font color=orange>'..Config.BoxMax[type]..'</font> صندوق</font>')
					end
				end, id,plate)
			end, function(data2, menu2)
				menu2.close()
			end)

		end,
		function(data, menu)
			menu.close()
		end)
	else

		ESX.UI.Menu.CloseAll()
		local elements = {}
		local type = getshoptype(id)

		for i=1, #Config.Items[type], 1 do
			if Config.Items[type][i].Storge == nil or Config.Items[type][i].Storge ~= false then
				table.insert(elements, {labels =  Config.Items[type][i].label, label =  '<font color=white> '.. Config.Items[type][i].label .. '<font color=red> | <font color=green> $<font color=white>' .. Config.Items[type][i].price..'<font color=red> | <font color=orange>'..Config.Items[type][i].count..'</font><font color=gray> في الصندوق </font>',	value = Config.Items[type][i].item, price = Config.Items[type][i].price})
			end
		end

		ESX.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'shipitem',
		{
			title    = '<font color=#ffffff>المستودع العام - <font color=orange>طلب منتجات جديدة',
			align    = 'bottom-right',
			elements = elements
		},
		function(data, menu)
			menu.close()
			ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'krille', {
				title = 'كم تريد شراء؟'
			}, function(data2, menu2)
				menu2.close()
				ESX.TriggerServerCallback('esx_shops2:canorder', function(canorder)
					if (canorder + tonumber(data2.value)) <= Config.BoxMax[type] then
						TriggerServerEvent('esx_kr_shop:MakeShipment', id, data.current.value, data.current.price, tonumber(data2.value), data.current.labels)
					else
						ESX.ShowNotification('<font color=red>لا يمكن طلب أكثر من <font color=orange>'..Config.BoxMax[type]..'</font> صندوق</font>')
					end
				end, id)
			end, function(data2, menu2)
				menu2.close()
			end)

		end,
		function(data, menu)
			menu.close()
		end)
	end
end


function TakeItem(number2,plate)
	if plate then
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getTruckItems', function(result)

			for i=1, #result, 1 do
				if result[i].count > 0 then
					table.insert(elements, {label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'removeitem', ItemName = result[i].item})
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد منتجات بالمتجر' })
			end

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'takeitem',
			{
				title    = 'إدارة المتجر',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				local name = data.current.ItemName

				if data.current.value == 'removeitem' then
					menu.close()
					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'howmuch', {
						title = 'كم تريد أن تأخذ؟'
					}, function(data2, menu2)

						local count = tonumber(data2.value)
						menu2.close()
						TriggerServerEvent('esx_kr_shops:RemoveItemFromShop', number2, count, name, plate)

					end, function(data2, menu2)
						menu2.close()
					end)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, plate)

	else

		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

			for i=1, #result, 1 do
				if result[i].count > 0 then
					table.insert(elements, {label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'removeitem', ItemName = result[i].item})
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد منتجات بالمتجر' })
			end


			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'takeitem',
			{
				title    = 'إدارة المتجر',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				local name = data.current.ItemName

				if data.current.value == 'removeitem' then
					menu.close()
					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'howmuch', {
						title = 'كم تريد أن تأخذ؟'
					}, function(data2, menu2)

						local count = tonumber(data2.value)
						menu2.close()
						TriggerServerEvent('esx_kr_shops:RemoveItemFromShop', number2, count, name)

					end, function(data2, menu2)
						menu2.close()
					end)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, number2)
	end

end

--resellitem
function resellitem(number2,plate)

	if plate then
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getTruckItems', function(result)

			for i=1, #result, 1 do
				if result[i].count > 0 then
					table.insert(elements, {label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'resellitem', ItemName = result[i].item})
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد منتجات مضافة' })
			end

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'resellitem',
			{
				title    = 'تغيير سعر سلعة',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				local name = data.current.ItemName

				if data.current.value == 'resellitem' then
					menu.close()
					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'resellitem2', {
						title = 'السعر الجديد'
					}, function(data2, menu2)

						local count = ESX.Math.Round(tonumber(data2.value))
						menu2.close()
						TriggerServerEvent('esx_kr_shops:resellItem', number2, count, name,plate)
						ESX.ShowNotification('<font color=green>تم تغيير سعر المنتج بنجاح</font>')

					end, function(data2, menu2)
						menu2.close()
					end)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, plate)
	else
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getShopItems', function(result)

			for i=1, #result, 1 do
				if result[i].count > 0 then
					table.insert(elements, {label = '<font color=gray>'..result[i].label..'</font> | <font color=orange>'..result[i].count..'</font><font color=gray> في المتجر</font> | <font color=green>$'..result[i].price..'</font>', value = 'resellitem', ItemName = result[i].item})
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد منتجات بالمتجر' })
			end


			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'resellitem',
			{
				title    = 'تغيير سعر سلعة',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				local name = data.current.ItemName

				if data.current.value == 'resellitem' then
					menu.close()
					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'resellitem2', {
						title = 'السعر الجديد'
					}, function(data2, menu2)

						local count = ESX.Math.Round(tonumber(data2.value))
						menu2.close()
						TriggerServerEvent('esx_kr_shops:resellItem', number2, count, name)
						ESX.ShowNotification('<font color=green>تم تغيير سعر المنتج بنجاح</font>')

					end, function(data2, menu2)
						menu2.close()
					end)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, number2)
	end
end


function PutItem(number2,plate)
	if plate then
		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getInventory', function(result)
			for i=1, #result.items, 1 do

				local invitem = result.items[i]
				for i = 1, #Config.Items["truck"], 1 do
					if Config.Items["truck"][i].item == invitem.name then
						if invitem.count > 0 then
							table.insert(elements, { label = '<font color=gray>'..invitem.label .. '</font> | <font color=orange>' .. invitem.count .. '</font><font color=gray> في الحقيبة </font>', count = invitem.count, name = invitem.name})
						end
					end
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد أغراض بالحقيبة' })
			end

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'putitem',
			{
				title    = 'إدارة المتجر',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)

				local itemName = data.current.name
				local invcount = data.current.count

				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
					title = _U('how_much')
				}, function(data2, menu2)

					local count = tonumber(data2.value)

					if count > invcount then
						ESX.ShowNotification('<font color=red>لا يمكنك بيع أكثر مما تملك')
						menu2.close()
						menu.close()
					else
						menu2.close()
						menu.close()

						for i = 1, #Config.Items["truck"], 1 do
							if Config.Items["truck"][i].item == itemName then
								if Config.Items["truck"][i].instore == nil or Config.Items["truck"][i].instore ~= false then
									local ccount = (Config.Items["truck"][i].count * count)
									local tyyppeeep = Config.Items["truck"][i].type or false
									local weaponsname = ESX.GetWeaponLabel(Config.Items["truck"][i].itemConvert) or false
									local levveell = 0

									if Config.Items["truck"][i].info then
										levveell = Config.Items["truck"][i].info.xp
									end

									if not tyyppeeep then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sellprice', {
											title = _U('set_price')
										}, function(data3, menu3)
											local done = false
											local price = tonumber(data3.value)
											menu2.close()
											menu3.close()
											menu.close()
											TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items["truck"][i].itemConvert, ccount, price, itemName, count, Config.Items["truck"][i].label, tyyppeeep, weaponsname, levveell,vehPlate)
										end)
									else
										TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items["truck"][i].itemConvert, ccount, Config.Items["truck"][i].info.price, itemName, count, Config.Items["truck"][i].label, tyyppeeep, weaponsname, levveell,vehPlate)
									end
									done = true
									break
								end
							end
						end

						if done ~= true then
							ESX.ShowNotification('<font color=red>لا يمكن عرض هذه السلعة</font>')
						end
					end
				end,
				function(data3, menu3)
					menu3.close()
				end)
			end,
			function(data2, menu2)
				menu2.close()
			end)
		end, function(data, menu)
			menu.close()
		end)
	else
		local type = getshoptype(number2)

		local elements = {}

		ESX.TriggerServerCallback('esx_kr_shop:getInventory', function(result)
			for i=1, #result.items, 1 do

				local invitem = result.items[i]

				if invitem.count > 0 then
					table.insert(elements, { label = '<font color=gray>'..invitem.label .. '</font> | <font color=orange>' .. invitem.count .. '</font><font color=gray> في الحقيبة </font>', count = invitem.count, name = invitem.name})
				end
			end

			if #elements == 0 then
				table.insert(elements, { label = 'لاتوجد منتجات بالمتجر' })
			end

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'putitem',
			{
				title    = 'إدارة المتجر',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)

				local itemName = data.current.name
				local invcount = data.current.count

				ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
					title = _U('how_much')
				}, function(data2, menu2)

					local count = tonumber(data2.value)

					if count > invcount then
						ESX.ShowNotification('<font color=red>لا يمكنك بيع أكثر مما تملك')
						menu2.close()
						menu.close()
					else
						menu2.close()
						menu.close()

						for i = 1, #Config.Items[type], 1 do
							if Config.Items[type][i].item == itemName then
								if Config.Items[type][i].instore == nil or Config.Items[type][i].instore ~= false then
									local ccount = (Config.Items[type][i].count * count)
									local tyyppeeep = Config.Items[type][i].type or false
									local weaponsname = ESX.GetWeaponLabel(Config.Items[type][i].itemConvert) or false
									local levveell = 0

									if Config.Items[type][i].info then
										levveell = Config.Items[type][i].info.xp
									end

									if not tyyppeeep then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sellprice', {
											title = _U('set_price')
										}, function(data3, menu3)
											local done = false
											local price = tonumber(data3.value)
											menu3.close()
											TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items[type][i].itemConvert, ccount, price, itemName, count, Config.Items[type][i].label, tyyppeeep, weaponsname, levveell)
										end)
									else
										TriggerServerEvent('esx_kr_shops:setToSell', number2, Config.Items[type][i].itemConvert, ccount, Config.Items[type][i].info.price, itemName, count, Config.Items[type][i].label, tyyppeeep, weaponsname, levveell)
									end
									done = true
									break
								end
							end
						end

						if done ~= true then
							ESX.ShowNotification('<font color=red>لا يمكن عرض هذه السلعة</font>')
						end
					end
				end,
				function(data3, menu3)
					menu3.close()
				end)
			end,
			function(data2, menu2)
				menu2.close()
			end)
		end, function(data, menu)
			menu.close()
		end)
	end
end

function secondsToClock(seconds)
	local seconds, hours, mins, secs = tonumber(seconds), 0, 0, 0

	if seconds <= 0 then
		return 0, 0
	else
		local hours = string.format("%02.f", math.floor(seconds / 3600))
		local mins = string.format("%02.f", math.floor(seconds / 60 - (hours * 60)))
		local secs = string.format("%02.f", math.floor(seconds - hours * 3600 - mins * 60))

		return mins, secs
	end
end

Citizen.CreateThread(function ()
	while not configready do Citizen.Wait(1000) end
	while true do
		local sleep = 1500

		local coords = GetEntityCoords(PlayerPedId())
		local letsleep = true

		for k,v in pairs(Config.Zones) do
			if(27 ~= -1 and GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < 20.0 ) then
				letsleep = false
				if v.Pos.red then
					DrawMarker(23, v.Pos.x, v.Pos.y, v.Pos.z + 0.05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.2, 180, 0, 0, 200, false, true, 2, false, false, false, false)
					DrawMarker(29, v.Pos.x, v.Pos.y, v.Pos.z + 1.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 180, 0, 0, 200, false, true, 2, false, false, false, false)
					sleep = 0
				elseif v.Pos.craft then
					DrawMarker(1, v.Pos.x, v.Pos.y, v.Pos.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.5, 1.5, 0.5, 180, 0, 0, 200, false, true, 2, false, false, false, false)
				else
					DrawMarker(23, v.Pos.x, v.Pos.y, v.Pos.z + 0.05, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.2, 0, 180, 0, 200, false, true, 2, false, false, false, false)
					DrawMarker(29, v.Pos.x, v.Pos.y, v.Pos.z + 1.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 180, 0, 200, false, true, 2, false, false, false, false)
					sleep = 0
				end
			end
		end

		if letsleep then
			Citizen.Wait(500)
		end
		Citizen.Wait(sleep)
	end
end)

Citizen.CreateThread(function ()
	while not configready do Citizen.Wait(1000) end
	while true do
		local sleep = 1500

		local coords = GetEntityCoords(PlayerPedId())
		for k,v in pairs(Config.Zones) do
			if GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < 5.0 then
				if v.Pos.red then
					if syncingTimer > 0 then
						local mt, st = secondsToClock(syncingTimer)
						local text = mt.. ':'..st..' ﺕﺎﻗﺮﺴﻟﺍ ﻦﻴﺑ ﺭﺎﻈﺘﻧﺍ ﺖﻗﻭ'
						Draw3DText(v.Pos.x, v.Pos.y, v.Pos.z , text, 1, 0.04, 0.04)
						sleep = 0
					end
				end
			end
		end
		Citizen.Wait(sleep)
	end
end)

RegisterNetEvent('napoly_AllCoolDownTimer')
AddEventHandler('napoly_AllCoolDownTimer', function(Timer)
	local second = 1000
	local minute = 60 * second
	local finelTime = Timer * minute
	syncingTimer = ESX.Math.Round(finelTime / 1000)
end)

Citizen.CreateThread(function ()
	while not configready do Citizen.Wait(1000) end
	while true do
		Citizen.Wait(1000)

		if syncingTimer > 0 then
			syncingTimer = syncingTimer - 1
		end
	end
end)

Citizen.CreateThread(function ()
	while not configready do Citizen.Wait(1000) end
	while true do
		local sleep = 500

		local coords      = GetEntityCoords(PlayerPedId())
		local isInMarker  = false
		local currentZone = nil
		local letsleep = true

		for k,v in pairs(Config.Zones) do
			if(GetDistanceBetweenCoords(coords, v.Pos.x, v.Pos.y, v.Pos.z, true) < 1.2) then
				letsleep = false
				sleep = 0
				isInMarker  = true
				currentZone = v.Pos.number
			end
		end

		local foodTruck = GetClosestVehicle(GetEntityCoords(PlayerPedId()), 7.0, 1951180813, 70)
		if DoesEntityExist(foodTruck) then
			local coords = GetOffsetFromEntityInWorldCoords(foodTruck, 1.2, -0.95, -1.0)
			-- if (GetVehicleDoorAngleRatio(foodTruck, 5)) ~= 0.0 then
			local distance = GetDistanceBetweenCoords(GetEntityCoords(PlayerPedId()), coords, true)
			if distance <= 7.0 then
				DrawMarker(23, coords.x, coords.y, coords.z + 0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.2, 0, 180, 0, 200, false, true, 2, false, false, false, false)
				DrawMarker(29, coords.x, coords.y, coords.z + 1.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 180, 0, 200, false, true, 2, false, false, false, false)
				sleep = 0
			end
			if GetDistanceBetweenCoords(GetEntityCoords(PlayerPedId()), coords, true) <= 1.5 then
				isInMarker = true
				currentZone = 2000
			end
			-- else
			-- 	-- DrawText3D({x=coords.x,y=coords.y,z=coords.z+2.0}, 'ﻖﻠﻐﻣ', {r=255,g=0,b=0,a=255}, 0.5)
			-- 	sleep = 0
			-- end
		end

		if (isInMarker and not HasAlreadyEnteredMarker) or (isInMarker and LastZone ~= currentZone) then
			sleep = 0
			HasAlreadyEnteredMarker = true
			LastZone                = currentZone
			if currentZone == 2000 then
				plate = ESX.Game.GetVehicleProperties(foodTruck).plate
			end
			TriggerEvent('esx_kr_shop:hasEnteredMarker', currentZone,plate)
		end

		if not isInMarker and HasAlreadyEnteredMarker then
			sleep = 0
			HasAlreadyEnteredMarker = false
			TriggerEvent('esx_kr_shop:hasExitedMarker', LastZone)
		end

		-- if letsleep then
		-- 	Citizen.Wait(500)
		-- end
		Citizen.Wait(sleep)
	end
end)

RegisterNetEvent('esx_kr_shops:setBlip')
AddEventHandler('esx_kr_shops:setBlip', function()

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips)

		if blips ~= nil then
			createBlip(blips)
		end
	end)
end)

--[[RegisterNetEvent('esx_kr_shops:setBlipMZAD')
AddEventHandler('esx_kr_shops:setBlipMZAD', function(isowner,number)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips)

		if blips ~= nil then
			createBlipMZAD(blips)
		end
	end)
end)]]

RegisterNetEvent('esx_kr_shops:removeBlip')
AddEventHandler('esx_kr_shops:removeBlip', function()

	for i=1, #displayedBlips do
		RemoveBlip(displayedBlips[i])
	end

end)

RegisterNetEvent('esx_kr_shops:refreshBlips')
AddEventHandler('esx_kr_shops:refreshBlips', function()
	for i=1, #displayedBlips do
		RemoveBlip(displayedBlips[i])
	end
	Citizen.Wait(500)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips, owned)

		if blips ~= nil then
			createBlip(blips)
			--createBlipMZAD(blips, owned)
		end
	end)
end)

AddEventHandler('playerSpawned', function(spawn)
	Citizen.Wait(500)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips, owned)

		if blips ~= nil then
			createBlip(blips)
			--createBlipMZAD(blips, owned)
		end
	end)
end)



Citizen.CreateThread(function()
	while not configready do Citizen.Wait(1000) end
	Citizen.Wait(500)

	ESX.TriggerServerCallback('esx_kr_shop:getOwnedBlips', function(blips, owned)

		if blips ~= nil then
			createBlip(blips)
			--createBlipMZAD(blips, owned)
		end
	end)
end)

function createBlip(blips)
	while not configready do Citizen.Wait(1000) end
	
	-- Remove existing blips
	for i=1, #displayedBlips do
			RemoveBlip(displayedBlips[i])
			displayedBlips[i] = nil
	end

	-- Create blips for all shops in Config.Zones (always visible)
	for k,v in pairs(Config.Zones) do
			-- Only create blips for shops with number less than or equal to 100
			local num = tonumber(v.Pos.number)
			if num and num <= 100 then
			local blip = AddBlipForCoord(vector3(v.Pos.x, v.Pos.y, v.Pos.z))
					if v.Type == 'market' then -- بقالة
							SetBlipSprite (blip, 52)
							SetBlipColour (blip, 2)
							SetBlipDisplay(blip, 4)
					elseif v.Type == 'pharmacie' then -- صيدلية
							SetBlipSprite (blip, 153)
							SetBlipColour (blip, 1)
							SetBlipDisplay(blip, 4)
					elseif v.Type == 'rts' then -- المطاعم
							SetBlipSprite (blip, 628)
							SetBlipColour (blip, 46)
							SetBlipDisplay(blip, 2)
					elseif v.Type == 'bar' then -- بار
							SetBlipSprite (blip, 93)
							SetBlipColour (blip, 50)
							SetBlipDisplay(blip, 4)
					elseif v.Type == 'weapons' then -- محل اسلحة
							SetBlipSprite (blip, 110)
							SetBlipColour (blip, 64)
							SetBlipDisplay(blip, 4)
					elseif v.Type == 'SodaMachine' then -- براد
							SetBlipSprite (blip, 619)
							SetBlipColour (blip, 64)
							SetBlipDisplay(blip, 5)
					end
					SetBlipScale  (blip, 0.8)
					SetBlipAsShortRange(blip, true)
					BeginTextCommandSetBlipName("STRING")
					AddTextComponentString(v.Type)
					EndTextCommandSetBlipName(blip)
					table.insert(displayedBlips, blip)
			end
	end

	-- Create additional blips for owned shops (with visibility toggle)
	for i=1, #blips, 1 do
			if blips[i].isVisible then
					for k,v in pairs(Config.Zones) do
							-- Only create blips for shops with number less than or equal to 100
							if v.Pos.number == blips[i].ShopNumber and v.Pos.number <= 100 then
									local ownerBlip = AddBlipForCoord(vector3(v.Pos.x, v.Pos.y, v.Pos.z))
									if v.Type == 'market' then
											SetBlipSprite (ownerBlip, 52)
									elseif v.Type == 'pharmacie' then
											SetBlipSprite (ownerBlip, 153)
									elseif v.Type == 'rts' then
											SetBlipSprite (ownerBlip, 628)
									elseif v.Type == 'bar' then
											SetBlipSprite (ownerBlip, 93)
									elseif v.Type == 'weapons' then
											SetBlipSprite (ownerBlip, 110)
									elseif v.Type == 'SodaMachine' then
											SetBlipSprite (ownerBlip, 619)
									end
									SetBlipColour (ownerBlip, 5)
									SetBlipDisplay(blip, 4)
									SetBlipScale  (ownerBlip, 1.2)
									SetBlipAsShortRange(ownerBlip, true)
									BeginTextCommandSetBlipName("STRING")
									AddTextComponentString(blips[i].ShopName .. " (متجرك)")
									EndTextCommandSetBlipName(ownerBlip)
									table.insert(displayedBlips, ownerBlip)
							end
					end
			end
	end
end


function createForSaleBlips()
	if showblip then

		IDBLIPS = {
			[1] = {x = 373.875,   y = 325.896,  z = 102.566, n = 1},
			[2] = {x = 2557.458,  y = 382.282,  z = 107.622, n = 2},
			[3] = {x = -3038.939, y = 585.954,  z = 6.908, n = 3},
			[4] = {x = -1487.553, y = -379.107,  z = 39.163, n = 4},
			[5] = {x = 1392.562,  y = 3604.684,  z = 33.980, n = 5},
			[6] = {x = -2968.243, y = 390.910,   z = 14.043, n = 6},
			[7] = {x = 2678.916,  y = 3280.671, z = 54.241, n = 7},
			[8] = {x = -48.519,   y = -1757.514, z = 28.421, n = 8},
			[9] = {x = 1163.373,  y = -323.801,  z = 68.205, n = 9},
			[10] = {x = -707.501,  y = -914.260,  z = 18.215, n = 10},
			[11] = {x = -1820.523, y = 792.518,   z = 137.118, n = 11},
			[12] = {x = 1698.388,  y = 4924.404,  z = 41.063, n = 12},
			[13] = {x = 1961.464,  y = 3740.672, z = 31.343, n = 13},
			[14] = {x = 1135.808,  y = -982.281,  z = 45.415, n = 14},
			[15] = {x = 25.88,     y = -1347.1,   z = 28.5, n = 15},
			[16] = {x = -1393.409, y = -606.624,  z = 29.319, n = 16},
			[17] = {x = 547.431,   y = 2671.710, z = 41.156, n = 17},
			[18] = {x = -3241.927, y = 1001.462, z = 11.830, n = 18},
			[19] = {x = 1166.024,  y = 2708.930,  z = 37.157, n = 19},
			[20] = {x = 1729.216,  y = 6414.131, z = 34.037, n = 20},
			[37] = {x = 2556.608, y = 2596.141, z = 38.11316, n = 20},
			[38] = {x = 1211.377, y = -1382.597, z = 35.40779, n = 20},
		}

		for i=1, #IDBLIPS, 1 do

			local blip2 = AddBlipForCoord(vector3(IDBLIPS[i].x, IDBLIPS[i].y, IDBLIPS[i].z))

			SetBlipSprite (blip2, 52)
			SetBlipDisplay(blip2, 4)
			SetBlipScale  (blip2, 0.8)
			SetBlipColour (blip2, 1)
			SetBlipAsShortRange(blip2, true)
			BeginTextCommandSetBlipName("STRING")
			AddTextComponentString('ID: ' .. IDBLIPS[i].n)
			EndTextCommandSetBlipName(blip2)
			table.insert(AllBlips, blip2)
		end

	else
		for i=1, #AllBlips, 1 do
			RemoveBlip(AllBlips[i])
		end
		ESX.UI.Menu.CloseAll()
	end
end
--ROBBERY

if Hamada.oldRobSystem then
	function Robbery(id)
		ESX.TriggerServerCallback('esx_kr_shop-robbery:getOnlinePolices', function(results)
			if results >= Config.RequiredPolices then
				TriggerEvent('esx_status:getStatus', 'drunk', function(status)
					if status.val >= 250000 then
						ESX.TriggerServerCallback('esx_kr_shop-robbery:getUpdates', function(isRobbingPossible, waitTimer, theShopmoney)
							if isRobbingPossible then
								TriggerServerEvent('esx_kr_shops-robbery:UpdateCanRob', id)
								TriggerServerEvent('esx_kr_shop-robbery:robshopmoney', id, theShopmoney)

								local coords = {
									x = Config.Zones[id].Object.x,
									y = Config.Zones[id].Object.y,
									z = Config.Zones[id].Object.z,
								}
								TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "جاري سرقة متجرك (" .. waitTimer..")", id)
								ESX.ShowNotification('<span style="color:#eea41c;"><center>تم بدأ السرقة </center></span><span style="color:#fc0304;"><center>يجب كسر الخزنة حتى تحصل على الأموال من السرقة</center></span> لايوجد وقت محدد ولكن انتبه قد تصل الجهات الأمنية في اي وقت او تقع بمشاكل مع صاحب المتجر<span style="color:#707070;"><br>في حال نجحت في كسر الخزنة ولم تحصل على المال ذلك يعني ان خزنة المتجر فارغة</span>', 15000)
								TriggerServerEvent('hamadashops:sendpolicenotif', Config.Zones[id].Object, waitTimer)
								ESX.Game.SpawnObject(**********, coords, function(safe)
									SetEntityHeading(safe,  Config.Zones[id].Object.h)
									FreezeEntityPosition(safe, true)
									if Hamada.SafeHealth  then
										SetEntityHealth(safe, Hamada.SafeHealth)
									else
										SetEntityHealth(safe, 7000)
									end

									OnRobbery = true
									Var = safe
									Id = id
									Coordss = coords
									Name = waitTimer
								end)
								-- ESX.ShowNotification('<span style="color:#eea41c;"></span>')
								TriggerServerEvent('esx_kr_shops-robbery:msg', "^3 اخـبـار | ^1عـــــــاجــــــل : ^3عملية سطو مسلح على متجر^1 " .." ".. waitTimer)
								TriggerServerEvent('esx_kr_shops-robbery:sendlog',('ادارة المتاجر'),'يتم حاليا عملية سطو مسلح على متجر','اسم المتجر:\n' ..waitTimer,10038562)
							else
								TriggerEvent("pNotify:SendNotification", {
									text = "<h1><center><font color=FFAE00><i>تم سرقة البقالة</i></font></h1>"..
									"</br><font size=5><p align=right><b>الوقت المتبقي حتى يمكن بدأ سرقة اخرى: ".."<font color=00A1FF>"..  math.floor(waitTimer) .."<font color=white> دقيقة",
									type = "alert",
									queue = "left",
									timeout = 7000,
									killer = true,
									theme = "gta",
									layout = "centerLeft"
								})
							end
						end, id)
					else
						ESX.ShowNotification('<font color=red> يجب أن تكون في حالة سكر بنسبة %25 لسرقة المتجر </font>')
					end
				end)
			else
				TriggerEvent("pNotify:SendNotification", {
					text = "<font size=6 color=FFAE00><p align=center><b>لايوجد عدد كافي من الشرطة</font>"..
					"</br><font size=5><p align=right><b>عدد الشرطة المطلوب لبدأ السرقة هو: ".."<font size=5 color=FF0E0E>"..Config.RequiredPolices.."</font>"..
					"</br><font size=5><p align=right><b>عدد الشرطة المتصلين بالسيرفر: ".."<font size=5 color=00EE4F>"..results.."</font>",
					type = "error",
					queue = "left",
					timeout = 7000,
					killer = true,
					theme = "gta",
					layout = "centerLeft"
				})
			end
		end)
	end
else
	function Robbery(id)
		ESX.TriggerServerCallback('esx_kr_shop-robbery:getOnlinePolices', function(results)
			if results >= Config.RequiredPolices then
				TriggerEvent('esx_status:getStatus', 'drunk', function(status)
					if status.val >= 250000 then
						ESX.TriggerServerCallback('esx_kr_shop-robbery:getUpdates', function(isRobbingPossible, waitTimer, theShopmoney)
							if isRobbingPossible then
								TriggerEvent("napoly_criminals_limit:start", vector3(Config.Zones[id].Object.x, Config.Zones[id].Object.y, Config.Zones[id].Object.z), 15, theShopmoney, function(outcome)
									-- if outcome == 1 then
									TriggerServerEvent('esx_kr_shops-robbery:UpdateCanRob', id)
									TriggerServerEvent('esx_kr_shop-robbery:robshopmoney', id, theShopmoney)

									local coords = {
										x = Config.Zones[id].Object.x,
										y = Config.Zones[id].Object.y,
										z = Config.Zones[id].Object.z,
									}
									-- ESX.ShowNotification('<span style="color:#eea41c;"></span>')
									ESX.ShowNotification('<span style="color:#eea41c;"><center>تم بدأ السرقة </center></span><span style="color:#fc0304;"><center>يجب كسر الخزنة حتى تحصل على الأموال من السرقة</center></span> لايوجد وقت محدد ولكن انتبه قد تصل الجهات الأمنية في اي وقت او تقع بمشاكل مع صاحب المتجر<span style="color:#707070;"><br>في حال نجحت في كسر الخزنة ولم تحصل على المال ذلك يعني ان خزنة المتجر فارغة</span>', 15000)
									TriggerServerEvent('hamadashops:sendpolicenotif', Config.Zones[id].Object, waitTimer)
									TriggerServerEvent('esx_addons_gcphone:startCall', 'police', "يتم سرقة بقالة " .. waitTimer .. '\'عند الإحداثيات', {
										x = Config.Zones[id].Object.x,
										y = Config.Zones[id].Object.y,
										z = Config.Zones[id].Object.z,
									})
									TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "جاري سرقة متجرك (" .. waitTimer..")", id)
									TriggerServerEvent('esx_kr_shops-robbery:msg', "^3 اخـبـار | ^1عـــــــاجــــــل : ^3عملية سطو مسلح على متجر^1 " .." ".. waitTimer)
									TriggerServerEvent('esx_kr_shops-robbery:sendlog',('ادارة المتاجر'),'يتم حاليا عملية سطو مسلح على متجر','اسم المتجر:\n' ..waitTimer,10038562)
									ESX.Game.SpawnObject(**********, coords, function(safe)
										SetEntityHeading(safe, Config.Zones[id].Object.h)
										FreezeEntityPosition(safe, true)
										if Hamada.SafeHealth  then
											SetEntityHealth(safe, Hamada.SafeHealth)
										else
											SetEntityHealth(safe, 25500)
										end

										OnRobbery = true
										Var = safe
										Id = id
										Coordss = coords
										Name = waitTimer
									end)
									-- else
									-- 	TriggerEvent("pNotify:SendNotification", {
									-- 		text = "<font size=6 color=FFAE00><p align=center><b>هنالك سرقه اخرى</font>"..
									-- 		"</br><font size=5><p align=right><b>يجب عليك الانتظار</font>",
									-- 		type = "error",
									-- 		queue = "left",
									-- 		timeout = 7000,
									-- 		killer = true,
									-- 		theme = "gta",
									-- 		layout = "centerLeft"
									-- 	})
									-- end
								end)
							else
								TriggerEvent("pNotify:SendNotification", {
									text = "<h1><center><font color=FFAE00><i>تم سرقة البقالة</i></font></h1>"..
									"</br><font size=5><p align=right><b>الوقت المتبقي حتى يمكن بدأ سرقة اخرى: ".."<font color=00A1FF>"..  math.floor(waitTimer) .."<font color=white> دقيقة",
									type = "alert",
									queue = "left",
									timeout = 7000,
									killer = true,
									theme = "gta",
									layout = "centerLeft"
								})
							end
						end, id)
					else
						ESX.ShowNotification('<font color=red> يجب أن تكون في حالة سكر بنسبة %25 لسرقة المتجر </font>')
					end
				end)
			else
				TriggerEvent("pNotify:SendNotification", {
					text = "<font size=6 color=FFAE00><p align=center><b>لايوجد عدد كافي من الشرطة</font>"..
					"</br><font size=5><p align=right><b>عدد الشرطة المطلوب لبدأ السرقة هو: ".."<font size=5 color=FF0E0E>"..Config.RequiredPolices.."</font>"..
					"</br><font size=5><p align=right><b>عدد الشرطة المتصلين بالسيرفر: ".."<font size=5 color=00EE4F>"..results.."</font>",
					type = "error",
					queue = "left",
					timeout = 7000,
					killer = true,
					theme = "gta",
					layout = "centerLeft"
				})
			end
		end)
	end
end

function Draw3DText(x,y,z,textInput,fontId,scaleX,scaleY)
	local px,py,pz=table.unpack(GetGameplayCamCoords())
	local dist = GetDistanceBetweenCoords(px,py,pz, x,y,z, 1)
	local scale = (1/dist)*20
	local fov = (1/GetGameplayCamFov())*100
	local scale = scale*fov
	SetTextScale(scaleX*scale, scaleY*scale)
	RegisterFontFile('A9eelsh')
	fontId = RegisterFontId('A9eelsh')
	SetTextFont(fontId)
	SetTextProportional(1)
	SetTextColour(189, 162, 49, 255)
	SetTextDropshadow(1, 1, 1, 1, 255)
	SetTextEdge(2, 0, 0, 0, 150)
	SetTextDropShadow()
	SetTextOutline()
	SetTextEntry("STRING")
	SetTextCentre(1)
	AddTextComponentString(textInput)
	SetDrawOrigin(x,y,z+2, 0)
	DrawText(0.0, 0.0)
	ClearDrawOrigin()
end



if Hamada.oldRobSystem then

	Citizen.CreateThread(function()
		while true do
			Wait(0)
			local playerpos = GetEntityCoords(PlayerPedId())
			local letsleep = true
			if OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) <= 15 then
				letsleep = false
				local hp = GetEntityHealth(Var)
				TriggerEvent("mt:missiontext", '<font face="A9EELSH">ﺔﻧﺰﺨﻟﺍ ﺮﺴﻛ:~r~ ' .. hp/100 .. "%", 1000)


				local Stealing = false
				local StealingTime = 0

				Citizen.CreateThread(function()
					while true do
						Citizen.Wait(1000)

						if Stealing then
							if StealingTime > 0 then
								StealingTime = StealingTime - 1
							else
								Stealing = false
							end
						end
					end
				end)

				if hp == 0 then
					OnRobbery = false
					TriggerServerEvent('esx_kr_shops-robbery:GetReward', Id, securityToken)
					TriggerServerEvent("esx_kr_shops-robbery:NotifyOwner", '<font color=red>تم سرقة متجرك <font color=gray>(' .. Name ..')<font color=red> للأسف', Id, 2, Name)
					DeleteEntity(Var)
				end

			elseif OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) >= 15 then
				letsleep = false
				OnRobbery = false
				DeleteEntity(Var)
				TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "<font color=red>محاولة سرقة في متجرك <font color=gray>(" .. Name .. ')<font color=green> فاشلة', Id, 3, Name)
				ESX.ShowNotification(_U("robbery_cancel"))
			end

			if letsleep then
				Citizen.Wait(500)
			end
		end
	end)
else
	Citizen.CreateThread(function()
		while true do
			Wait(0)
			local playerpos = GetEntityCoords(PlayerPedId())
			local letsleep = true
			if OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) <= 15 then
				letsleep = false
				local hp = GetEntityHealth(Var)
				TriggerEvent("mt:missiontext", '<font face="A9eelsh">ﺔﻧﺰﺨﻟﺍ ﺮﺴﻛ:~r~ ' .. hp/25 .. "%", 1000)

				if hp == 0 then
					OnRobbery = false
					TriggerEvent('esx_misc:updatePromotionStatus', 'StartRobbery', false)
					--TriggerServerEvent('esx_kr_shops-robbery:GetReward', Id, securityToken)
					if Name then
						TriggerServerEvent("esx_kr_shops-robbery:NotifyOwner", '<font color=red>تم سرقة متجرك <font color=gray>(' .. Name ..')<font color=red> للأسف', Id, 2, Name)
					end
					DeleteEntity(Var)
					TriggerServerEvent("napoly_criminals_limit:SV:donerobbery")
					TriggerServerEvent('esx_kr_shops-robbery:stopRobb')
					TriggerEvent("pNotify:SendNotification", {
						text = "<h1><center><font color=FFAE00><i>تم كسر الخزنة بنجاح</i></font></h1>"..
						"</br><font size=5><p align=right><b>توجه إلى الموقع على الخريطة للحصول على المسروقات",
						type = "alert",
						queue = "left",
						timeout = 7000,
						killer = true,
						theme = "gta",
						layout = "centerLeft"
					})
				end

			elseif OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) >= 15 then
				letsleep = false
				OnRobbery = false
				TriggerEvent('napoly_criminals_limit:stop')
				TriggerEvent('esx_misc:updatePromotionStatus', 'StartRobbery', false)
				DeleteEntity(Var)
				TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "<font color=red>محاولة سرقة في متجرك <font color=gray>(" .. Name .. ')<font color=green> فاشلة', Id, 3, Name)
				ESX.ShowNotification(_U("robbery_cancel"))
			end

			if letsleep then
				Citizen.Wait(500)
			end
		end
	end)
end

AddEventHandler('esx:onPlayerDeath', function(data)
	if OnRobbery then
		OnRobbery = false
		TriggerEvent('napoly_criminals_limit:stop')
		TriggerEvent('esx_misc:updatePromotionStatus', 'StartRobbery', false)
		DeleteEntity(Var)
		if Name then
			TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "<font color=red>محاولة سرقة في متجرك <font color=gray>(" .. Name .. ')<font color=green> فاشلة', Id, 3, Name)
		end
		TriggerEvent('napoly_criminals_limit:CLclose')
		ESX.ShowNotification(_U("robbery_cancel"))
	end
end)

RegisterNetEvent('hamada:addonshop', function()
	local playerCoords = GetEntityCoords(PlayerPedId())
	Var = GetClosestObjectOfType(playerCoords.x, playerCoords.y, playerCoords.z, 30.0, **********, false, false, false)
	OnRobbery = true
	Coordss = GetEntityCoords(PlayerPedId())
end)

-- Citizen.CreateThread(function()
-- while true do
-- Wait(0)
-- local playerpos = GetEntityCoords(PlayerPedId())
-- if OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) <= 15 then

-- local hp = GetEntityHealth(Var)
-- TriggerEvent("mt:missiontext", "<FONT FACE='A9eelsh'>ﻞﻔﻘﻟﺍ ﺮﺴﻛ:~r~ " .. hp/25 .. "%", 1000)

-- if hp == 0 then
-- OnRobbery = false
-- TriggerServerEvent('esx_kr_shops-robbery:GetReward', Id)
-- TriggerServerEvent("esx_kr_shops-robbery:NotifyOwner", 'السرقة على متجرك <span style="color:orange;">(' .. Name ..')</span> كان ناجحًا للأسف!', Id)
-- DeleteEntity(Var)
-- TriggerServerEvent("napoly_criminals_limit:SV:donerobbery")
-- end

-- elseif OnRobbery and GetDistanceBetweenCoords(playerpos.x, playerpos.y, playerpos.z, Coordss.x, Coordss.y, Coordss.z, true) >= 15 then
-- OnRobbery = false
-- DeleteEntity(Var)
-- TriggerServerEvent('esx_kr_shops-robbery:NotifyOwner', "السرقة على متجرك <span style='color:orange;'>(" .. Name .. ')</span> لم يكن ناجحًا!', Id)
-- ESX.ShowpNotifyNotification(_U("robbery_cancel"))
-- end
-- end
-- end)

RegisterNetEvent("mt:missiontext") -- credits: https://github.com/schneehaze/fivem_missiontext/blob/master/MissionText/missiontext.lua
AddEventHandler("mt:missiontext", function(text, time)
	ClearPrints()
	SetTextEntry_2("STRING")
	AddTextComponentString(text)
	DrawSubtitleTimed(time, 1)
end)



--------------------
--	   Storge	  --
--------------------

Citizen.CreateThread( function()
	while not configready do Citizen.Wait(1000) end

	while true do
		local letsleep = true
		for	i = 1, #Config.Storge, 1 do
			local ped = PlayerPedId()
			local PedLocation = GetEntityCoords(ped)
			local MarkkerNumbber = 10
			local raduis = GetDistanceBetweenCoords(PedLocation, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z, true)
			if raduis <= 35.0 then
				letsleep = false
				if i == 1 then MarkkerNumbber = 11 end;if i == 2 then MarkkerNumbber = 12 end;if i == 3 then MarkkerNumbber = 13 end;if i == 4 then MarkkerNumbber = 14 end;if i == 5 then MarkkerNumbber = 15 end;if i == 6 then MarkkerNumbber = 16 end;if i == 7 then MarkkerNumbber = 17 end;if i == 8 then MarkkerNumbber = 18 end;if i == 9 then MarkkerNumbber = 19 end;
				DrawMarker(MarkkerNumbber, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z + 0.2, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 0.5, 0.5, 0.5, 0, 255, 0, 150, false, true, 2, false, false, false, false)
				DrawMarker(25, Config.Storge[i].pos.x, Config.Storge[i].pos.y, Config.Storge[i].pos.z - 1.0, 0.0, 0.0, 0.0, 0, 0.0, 0.0, 1.0, 1.0, 1.0, 0, 255, 0, 150, false, true, 2, false, false, false, false)
				if raduis <= 1.2 then
					ESX.ShowHelpNotification('ﺔﻤﺋﺎﻘﻟﺍ ﺢﺘﻔﻟ ~y~E~w~ ﻂﻐﺿﺍ')
					if IsControlJustReleased(0, Keys['E']) then
						ESX.UI.Menu.CloseAll()
						exports['pogressBar']:drawBar(1500, 'جار التنفيذ')
						Citizen.SetTimeout(1300, function()
							ChakaBoss()
						end)
					end
				end
			end
		end

		if letsleep then
			Citizen.Wait(500)
		end

		Citizen.Wait(1)
	end
end)

function ChakaBoss()
	local elements = {}
	ESX.TriggerServerCallback('esx_shops2:GetOwnTruckNumber', function(data1)
		ESX.TriggerServerCallback('esx_shops2:GetOwnShopNumber', function(data2)

			for i=1, #data1, 1 do
				table.insert(elements, { label = '<font color=gray>'..data1[i].plate..'</font> | </font><font color=gray>إدارة المتنقل</font>',number = "2000", plate = data1[i].plate, owner = true})
			end

			for i=1, #data2, 1 do
				local type2 = getshoptype(data2[i].number)
				if type2 == "SodaMachine" then
					table.insert(elements, { label = '</font><font color=gray>'..data2[i].name..'</font> | <font color=gray> إدارة البراد </font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				elseif type2 == "market" then
					table.insert(elements, { label = '</font><font color=gray>'..data2[i].name..'</font> | <font color=gray> إدارة المتجر</font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				elseif type2 == "bar" then
					table.insert(elements, { label = '</font><font color=gray>'..data2[i].name..'</font> | <font color=gray> إدارة البار</font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				elseif type2 == "pharmacie" then
					table.insert(elements, { label = '</font><font color=gray>'..data2[i].name..'</font> | <font color=gray> إدارة الصيدلية</font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				elseif type2 == "rts" then
					table.insert(elements, { label = ' </font><font color=gray>'..data2[i].name..'</font> | <font color=gray>إدارة المـطـعـم </font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				elseif type2 == "weapons" then
					table.insert(elements, { label = ' </font><font color=gray>'..data2[i].name..'</font> | <font color=gray>إدارة محل الأسلحة </font>' , number = data2[i].number, plate = false, owner = data2[i].owner})
				end
			end

			ESX.UI.Menu.Open("default", GetCurrentResourceName(), "Example_Menu", {
				title    = 'إدارة المتجر',
				align    = 'bottom-right', -- top-left | top-right | bottom-left | bottom-right | center |
				elements = elements
			}, function(data3,menu3) -- OnSelect Function
				--- for a simple element
				if data3.current.plate ~= false then
					menu3.close()
					OpenBoss2(data3.current.number, data3.current.owner,data3.current.plate)
				else
					menu3.close()
					OpenBoss2(data3.current.number, data3.current.owner)
				end
			end, function(data, menu) -- Cancel Function
				menu.close() -- close menu
			end)

		end)
	end)
end

function OpenBoss2(number2, isowner,plate)

	if plate then
		ESX.UI.Menu.CloseAll()
		ESX.TriggerServerCallback('esx_kr_shop:getOwnedTrucks', function(data)

			local elements = {}

			table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
			-- table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
			-- table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
			table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
			table.insert(elements, {label = 'طلب منتجات جديدة', value = 'buy'})
			table.insert(elements, {label = '<font color=#1B76F9> تتبع الطلبات ', value = 'shipments'})
			-- table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
			-- table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
			-- table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'boss',
			{
				title    = 'إدارة المتنقل <font color=gray>'..data[1].ShopName..'</font>',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				if data.current.value == 'putitem' then
					PutItem(number2,plate)
				elseif data.current.value == 'takeitem' then
					TakeItem(number2,plate)
				elseif data.current.value == 'takemoney' then


					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
						title = 'كم تريد أن تسحب'
					}, function(data2, menu2)

						local amount = tonumber(data2.value)

						TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number2, plate)

						menu2.close()

					end,
					function(data2, menu2)
						menu2.close()
					end)

				elseif data.current.value == 'putmoney' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
						title = 'كم تريد أن تودع؟'
					}, function(data3, menu3)
						local amount = tonumber(data3.value)
						TriggerServerEvent('esx_kr_shops:addMoney', amount, number2,plate)
						menu3.close()
					end,
					function(data3, menu3)
						menu3.close()
					end)

				elseif data.current.value == 'sell' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
						title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
					}, function(data4, menu4)

						if data4.value == 'نعم' then
							TriggerServerEvent('esx_kr_shops:SellShop', number2, securityToken)
							menu4.close()
						end
					end,
					function(data4, menu4)
						menu4.close()
					end)

				elseif data.current.value == 'changename' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
						title = 'ماذا تريد تسمية متجرك؟'
					}, function(data5, menu5)

						TriggerServerEvent('esx_kr_shops:changeName', number2, data5.value)
						menu5.close()
					end,
					function(data5, menu5)
						menu5.close()
					end)

					----------------------------------
				elseif data.current.value == 'shipments' then
					ESX.UI.Menu.CloseAll()
					GetAllShipments(number2,plate)

				elseif data.current.value == 'buy' then
					ESX.UI.Menu.CloseAll()
					OpenShipmentDelivery(number2,plate)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, plate)
	else
		ESX.UI.Menu.CloseAll()
		ESX.TriggerServerCallback('esx_kr_shop:getOwnedShop', function(data)

			local elements = {}

			table.insert(elements, {label = '<font color=#999999> رصيد المتجر : <font color=#00EE4F>$<font color=#ffffff>' .. data[1].money ,    value = ''})
			-- table.insert(elements, {label = 'اضافة سلعة للبيع', value = 'putitem'})
			-- table.insert(elements, {label = 'سحب سلعة من المتجر',    value = 'takeitem'})
			table.insert(elements, {label = '<font color=orange>ايداع</font> نقود في رصيد المتجر',    value = 'putmoney'})
			table.insert(elements, {label = 'طلب منتجات جديدة', value = 'buy'})
			table.insert(elements, {label = '<font color=#1B76F9> تتبع الطلبات ', value = 'shipments'})
			-- table.insert(elements, {label = '<font color=orange>سحب</font> نقود من رصيد المتجر',    value = 'takemoney'})
			-- table.insert(elements, {label = '<font color=#F98A1B>تغيير اسم المتجر مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. Config.ChangeNamePrice,    value = 'changename'})
			-- table.insert(elements, {label = '<font color=#CB120D>بيع متجرك مقابل : <font color=#00EE4F>$<font color=#ffffff>' .. math.floor(data[1].ShopValue / Config.SellValue),   value = 'sell'})

			ESX.UI.Menu.Open(
			'default', GetCurrentResourceName(), 'boss',
			{
				title    = 'إدارة متجر <font color=gray>'..data[1].ShopName..'</font>',
				align    = 'bottom-right',
				elements = elements
			},
			function(data, menu)
				if data.current.value == 'putitem' then
					PutItem(number2)
				elseif data.current.value == 'takeitem' then
					TakeItem(number2)
				elseif data.current.value == 'takemoney' then


					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'takeoutmoney', {
						title = 'كم تريد أن تسحب'
					}, function(data2, menu2)

						local amount = tonumber(data2.value)

						TriggerServerEvent('esx_kr_shops:takeOutMoney', amount, number2, securityToken)

						menu2.close()

					end,
					function(data2, menu2)
						menu2.close()
					end)

				elseif data.current.value == 'putmoney' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'putinmoney', {
						title = 'كم تريد أن تودع؟'
					}, function(data3, menu3)
						local amount = tonumber(data3.value)
						TriggerServerEvent('esx_kr_shops:addMoney', amount, number2)
						menu3.close()
					end,
					function(data3, menu3)
						menu3.close()
					end)

				elseif data.current.value == 'sell' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'sell', {
						title = 'اكتب: ( نعم ) بدون أقواس للتأكيد'
					}, function(data4, menu4)

						if data4.value == 'نعم' then
							TriggerServerEvent('esx_kr_shops:SellShop', number2, plate)
							menu4.close()
						end
					end,
					function(data4, menu4)
						menu4.close()
					end)

				elseif data.current.value == 'changename' then
					ESX.UI.Menu.CloseAll()

					ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'changename', {
						title = 'ماذا تريد تسمية متجرك؟'
					}, function(data5, menu5)

						TriggerServerEvent('esx_kr_shops:changeName', number2, data5.value)
						menu5.close()
					end,
					function(data5, menu5)
						menu5.close()
					end)

					----------------------------------
				elseif data.current.value == 'shipments' then
					ESX.UI.Menu.CloseAll()
					GetAllShipments(number2)

				elseif data.current.value == 'buy' then
					ESX.UI.Menu.CloseAll()
					OpenShipmentDelivery(number2)
				end
			end,
			function(data, menu)
				menu.close()
			end)
		end, number2)
	end
end

function OpenShipments()

end

function OpenShopCenter()
	ESX.UI.Menu.CloseAll()
	local elements = {}

	if showblip then
		table.insert(elements, {label = '<span  style="color:#FF0E0E">اخفاء جميع المتاجر من الخريطة</span>', value = 'removeblip'})
	else
		table.insert(elements, {label = '<span  style="color:#999999">اظهار جميع المتاجر في الخريطة</span>', value = 'showblip'})
	end

	ESX.TriggerServerCallback('esx_kr_shop:getShopList', function(ress,ress2)
		ESX.TriggerServerCallback('esx_shops2:checkmzadshops', function(mzadshops,mazadtrucks)

			for i=1, #ress, 1 do

				for k,v in pairs(Config.Zones) do
					if Config.Zones[k].Pos.number == ress[i].ShopNumber then
						if mzadshops[i].isMazad == true then
							local chakamoney = tonumber(ress[i].ShopValue + mzadshops[i].chakaMoney)
							table.insert(elements, {label = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد  '..ShopLabell2[Config.Zones[k].Type]..' <font color=red>'.. ress[i].ShopNumber ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(chakamoney)..' <font color=green> $<font color=white> ] <span>', value = '3rd', number = ress[i].ShopNumber,money = chakamoney, type = Config.Zones[k].Type, data = ress[i]})
						else
							table.insert(elements, {label = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=gray> غير متاح للبيع قيد المراجعة من الرقابة  -  <font color=white> '..ShopLabell2[Config.Zones[k].Type]..' <font color=red>'.. ress[i].ShopNumber ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(ress[i].ShopValue)..' <font color=green> $<font color=white> ] <span>', value = '3rd', number = ress[i].ShopNumber,money = ress[i].ShopValue, type = Config.Zones[k].Type, data = ress[i]})
						end
						-- table.insert(elements, {label = '<font color=gray> غير غير متاح للبيع قيد المراجعة من الرقابة</font>', value = '3rd', number = ress[i].ShopNumber, type = Config.Zones[k].Type, data = ress[i]})
					end
				end
			end
			for i=1, #ress2, 1 do
				if mazadtrucks[i].isMazad == true then
					local chakamoney = tonumber(ress2[i].ShopValue + mazadtrucks[i].chakaMoney)
					table.insert(elements, {label = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد  متنقل <font color=red>'.. ress2[i].plate ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(chakamoney)..' <font color=green> $<font color=white> ] <span>', value = '3rd', plate = ress2[i].plate, data = ress2[i],money = chakamoney})
				else
					table.insert(elements, {label = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=gray> غير متاح للبيع قيد المراجعة من الرقابة  -  <font color=white> متنقل <font color=red>'.. ress2[i].plate ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(ress2[i].ShopValue)..' <font color=green> $<font color=white> ] <span>', value = '3rd', plate = ress2[i].plate, data = ress2[i],money = ress2[i].ShopValue})

				end
			end

			ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad',
			{
				title    = 'إدارة المتاجر',
				align = 'top-left',
				elements = elements
			},
			function(data, menu)
				local action = data.current.value
				if action == 'removeblip' then
					showblip = false
					createForSaleBlips()
					menu.close()
				end
				if action == 'showblip' then
					showblip = true
					createForSaleBlips()
					menu.close()
				end
				if action == '3rd' then
					if ESX.PlayerData.job.name == 'admin' then
						--------------------------------------
						--------------------------------------
						--------------------------------------
						if data.current.number then
							ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss)
								local elements5 = {}

								if resssss.done then
									table.insert(elements5, {label = '<font color=green>عرض</font>', value = '3rd2'})
									table.insert(elements5, {label = '<font color=red>رجوع</font>', value = 'cancel'})
								else
									table.insert(elements5, {label = '<span style="color:686868;">بيانات المشاركين</span>'})
									table.insert(elements5, {label = '<font color=gray>أعلى مزايدة : $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
									table.insert(elements5, {label = '<font color=green>: ادخل قيمة المزايدة</font>', value = '3rd3222222'})
									table.insert(elements5, {label = 'إنهاء المزاد واعتماده', value = '3rd32'})
									table.insert(elements5, {label = '<font color=red>إلغاء المزاد</font>', value = '3rd3'})
									table.insert(elements5, {label = '<span style="color:BDA231;">تحديث بيانات المزاد</span>', value = "refreshA" })
								end

								ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									title    = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد  '..ShopLabell2[data.current.type]..' <font color=red>'.. data.current.number ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(data.current.money)..' <font color=green> $<font color=white> ] <span>',
									align = 'top-left',
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'add', securityToken)
										ESX.UI.Menu.CloseAll()
										exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
										Citizen.SetTimeout(1000, function()
											OpenShopCenter()
										end)
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'remove', securityToken)
										ESX.UI.Menu.CloseAll()
										exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
										Citizen.SetTimeout(1000, function()
											OpenShopCenter()
										end)
									elseif action2 == 'refreshA' then
										OpenShopCenter()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'close', securityToken)
										ESX.UI.Menu.CloseAll()
										exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
										Citizen.SetTimeout(1000, function()
											OpenShopCenter()
										end)
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = "المبلغ"
										}, function(data3, menu3)

											local amountttttt = tonumber(data3.value)

											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], amountttttt, 'playermazad', securityToken)
											ESX.UI.Menu.CloseAll()
											exports['pogressBar']:drawBar(1000, 'جار التنفيذ')
											Citizen.SetTimeout(1000, function()
												OpenShopCenter()
											end)
										end, function(data3, menu3)
											menu3.close()
										end)
									end
								end, function(data2, menu2)
									menu2.close()
								end)
							end, data.current.number)
						else
							ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss)
								local elements5 = {}

								if resssss.done then
									table.insert(elements5, {label = '<font color=green>عرض</font>', value = '3rd2'})
									table.insert(elements5, {label = '<font color=red>رجوع</font>', value = 'cancel'})
								else
									table.insert(elements5, {label = '<span style="color:686868;">بيانات المشاركين</span>'})
									table.insert(elements5, {label = '<font color=gray>أعلى مزايدة : $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
									table.insert(elements5, {label = '<font color=green>: ادخل قيمة المزايدة</font>', value = '3rd3222222'})
									table.insert(elements5, {label = 'إنهاء المزاد واعتماده', value = '3rd32'})
									table.insert(elements5, {label = '<font color=red>إلغاء المزاد</font>', value = '3rd3'})
									table.insert(elements5, {label = '<span style="color:BDA231;">تحديث بيانات المزاد</span>', value = "refreshA" })
								end
								ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									-- title    = 'التحكم بالمتنقل  رقم '..data.current.plate,
									title    = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد المتنقل <font color=red>'.. data.current.plate ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(data.current.money)..' <font color=green> $<font color=white> ] <span>',
									align = 'top-left',
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'add', true)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'remove', true)
										menu2.close()
									elseif action2 == 'refreshA' then
										OpenShopCenter()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'close', true)
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = "المبلغ"
										}, function(data3, menu3)

											local amountttttt = tonumber(data3.value)

											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤﻟﺍ", "المتنقل", amountttttt, 'playermazad', true)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
							end, data.current.plate,true)
						end
					else
						if data.current.number then
							ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss)
								local elements5 = {}

								if resssss.done then
									table.insert(elements5, {label = '<font color=green>عرض</font>', value = '3rd2'})
									table.insert(elements5, {label = '<font color=red>رجوع</font>', value = 'cancel'})
								else
									table.insert(elements5, {label = '<span style="color:686868;">بيانات المشاركين</span>'})
									table.insert(elements5, {label = '<font color=gray>أعلى مزايدة : $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
									table.insert(elements5, {label = '<font color=green>: ادخل قيمة المزايدة</font>', value = '3rd3222222'})
									table.insert(elements5, {label = 'إنهاء المزاد واعتماده', value = '3rd32'})
									table.insert(elements5, {label = '<font color=red>إلغاء المزاد</font>', value = '3rd3'})
									table.insert(elements5, {label = '<span style="color:BDA231;">تحديث بيانات المزاد</span>', value = "refreshA" })
								end
								ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									-- title    = 'التحكم بالمتنقل  رقم '..data.current.plate,
									title    = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد المتنقل <font color=red>'.. data.current.plate ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(data.current.money)..' <font color=green> $<font color=white> ] <span>',
									align = 'top-left',
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'add', true)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'remove', true)
										menu2.close()
									elseif action2 == 'refreshA' then
										OpenShopCenter()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤ", "متنقل", 0, 'close', true)
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = "المبلغ"
										}, function(data3, menu3)

											local amountttttt = tonumber(data3.value)

											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, "ﻞﻘﻨﺘﻤﻟﺍ", "المتنقل", amountttttt, 'playermazad', true)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
							end, data.current.plate,true)
							ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss)
								local elements5 = {}

								if resssss.done then
									table.insert(elements5, {label = '<font color=gray>لا يوجد مزاد على هذا ال'..ShopLabell2[data.current.type]..'</font>', value = ''})
								else
									table.insert(elements5, {label = '<font color=gray>أقل مبلغ للمزايدة : <font color=green>$<font color=white>'..ESX.Math.GroupDigits(100000)..'</font>', value = ''})
									table.insert(elements5, {label = '<font color=#d5a000>المزايدة</font>', value = '3rd3222222'})
								end
								ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									title    = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد  '..ShopLabell2[data.current.type]..' <font color=red>'.. data.current.number ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(data.current.money)..' <font color=green> $<font color=white> ] <span>',
									align = 'top-left',
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'add', securityToken)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'remove', securityToken)
										menu2.close()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'close', securityToken)
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = "المبلغ"
										}, function(data3, menu3)

											local amountttttt = tonumber(data3.value)

											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], amountttttt, 'playermazad', securityToken)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
							end, data.current.number)
						else
							ESX.TriggerServerCallback('esx_shops2:checkmazadstartornot', function(resssss)
								local elements5 = {}

								if resssss.done then
									table.insert(elements5, {label = '<font color=gray>لا يوجد مزاد على هذا ال'..ShopLabell2[data.current.type]..'</font>', value = ''})
								else
									table.insert(elements5, {label = '<font color=gray>أعلى مزايدة : $'..ESX.Math.GroupDigits(resssss.data.money)..'</font>', value = ''})
									table.insert(elements5, {label = '<font color=green>المزايدة</font>', value = '3rd3222222'})
								end
								ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'mazad2',
								{
									title    = '<span style="padding-left:15px;padding-right:15px;font-size: 0.8em;"><font color=#d5a000> مزاد المتنقل <font color=red>'.. data.current.plate ..'  </font> <font color=white>  [ '..ESX.Math.GroupDigits(data.current.money)..' <font color=green> $<font color=white> ] <span>',
									align = 'top-left',
									elements = elements5
								},
								function(data2, menu2)
									local action2 = data2.current.value

									if action2 == '3rd2' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'add', true)
										menu2.close()
									elseif action2 == 'cancel' then
										menu2.close()
									elseif action2 == '3rd3' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'remove', true)
										menu2.close()
									elseif action2 == '3rd32' then
										TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], 0, 'close', true)
										menu2.close()
									elseif action2 == '3rd3222222' then
										ESX.UI.Menu.Open('dialog', GetCurrentResourceName(), 'SSSSSSSSSS', {
											title = 'المزايدة من $'..ESX.Math.GroupDigits(Config.Mazad.L)..' إلى $'..ESX.Math.GroupDigits(Config.Mazad.H)
										}, function(data3, menu3)

											local amountttttt = tonumber(data3.value)

											TriggerServerEvent('esx_shops2:mazaddd', data.current.data, ShopLabell3[data.current.type], ShopLabell2[data.current.type], amountttttt, 'playermazad', true)
											menu3.close()
										end, function(data3, menu3)
											menu3.close()
										end)
									end

								end, function(data2, menu2)
									menu2.close()
								end)
							end, data.current.plate,true)
						end

						--------------------------------------
						--------------------------------------
						--------------------------------------
					end
				end

			end, function(data, menu)
				menu.close()
			end)
		end, ress,ress2)
	end)
end

---------------------
-- WEAPONS - CRAFT --
---------------------

Citizen.CreateThread( function()
	while true do
		local sleep = 1500
		if BLOCKINPUTCONTROL then
			sleep = 0
			DisableControlAction(0, 24, true) -- Attack
			DisableControlAction(0, 257, true) -- Attack 2
			DisableControlAction(0, 25, true) -- Aim
			DisableControlAction(0, 263, true) -- Melee Attack 1
			DisableControlAction(0, Keys['LEFTALT'], true)
			DisableControlAction(0, Keys['W'], true) -- W
			DisableControlAction(0, Keys['A'], true) -- A
			DisableControlAction(0, Keys['E'], true) -- A
			DisableControlAction(0, 31, true) -- S (fault in Keys table!)
			DisableControlAction(0, 30, true) -- D (fault in Keys table!)

			DisableControlAction(0, Keys['R'], true) -- Reload
			DisableControlAction(0, Keys['SPACE'], true) -- Jump
			DisableControlAction(0, Keys['Q'], true) -- Cover
			DisableControlAction(0, Keys['TAB'], true) -- Select Weapon
			DisableControlAction(0, Keys['F'], true) -- Also 'enter'?

			DisableControlAction(0, Keys['F1'], true) -- Disable phone
			DisableControlAction(0, Keys['F2'], true) -- Inventory
			DisableControlAction(0, Keys['F3'], true) -- Animations
			DisableControlAction(0, Keys['F6'], true) -- Job

			DisableControlAction(0, Keys['V'], true) -- Disable changing view
			DisableControlAction(0, Keys['C'], true) -- Disable looking behind
			DisableControlAction(0, Keys['X'], true) -- Disable clearing animation
			DisableControlAction(2, Keys['P'], true) -- Disable pause screen

			DisableControlAction(0, 59, true) -- Disable steering in vehicle
			DisableControlAction(0, 71, true) -- Disable driving forward in vehicle
			DisableControlAction(0, 72, true) -- Disable reversing in vehicle

			DisableControlAction(2, Keys['LEFTCTRL'], true) -- Disable going stealth

			DisableControlAction(0, 47, true)  -- Disable weapon
			DisableControlAction(0, 264, true) -- Disable melee
			DisableControlAction(0, 257, true) -- Disable melee
			DisableControlAction(0, 140, true) -- Disable melee
			DisableControlAction(0, 141, true) -- Disable melee
			DisableControlAction(0, 142, true) -- Disable melee
			DisableControlAction(0, 143, true) -- Disable melee
			DisableControlAction(0, 75, true)  -- Disable exit vehicle
			DisableControlAction(27, 75, true) -- Disable exit vehicle
		end
		Citizen.Wait(sleep)
	end
end)


function CraftingWeapons()
	ESX.TriggerServerCallback('esx_shops2:CraftWeap9923ons2', function(okk2)
		if okk2 then
			ESX.UI.Menu.Open( 'default', GetCurrentResourceName(), 'CRAFT',
			{
				title    = 'قائمة تصنيع سلاح',
				align = 'top-left',
				elements = {
					{ label = 'رشاش مايكرو', value = 'WEAPON_MICROSMG_box' },
					{ label = 'شوزن', value = 'WEAPON_PUMPSHOTGUN_box' },
				}
			},
			function(data2, menu2)
				ESX.TriggerServerCallback('esx_shops2:CraftWeap9923ons', function(okk)
					if okk then
						BLOCKINPUTCONTROL = true
						ESX.ShowNotification('تتم الآن عملية تصنيع سلاح</br><font size=4>النوع: '..data2.current.label..'</br><font color=orange>الرجاء الإنتظار</font></font>')
						TriggerEvent("pNotify:SendNotification", {
							text = "<font size=5 color=white><center><b>"..msgg,
							type = 'success',
							queue = left,
							timeout = Config.WeaponCraftTime,
							killer = false,
							theme = "gta",
							layout = "CenterLeft",
						})
						ESX.UI.Menu.CloseAll()
						Citizen.Wait(Config.WeaponCraftTime)
						BLOCKINPUTCONTROL = false
						ESX.ShowNotification('<font color=green>تم تصنيع السلاح بنجاح</font>')
					end
				end, data2.current.value)
			end, function(data2, menu2)
				menu2.close()
			end)
		else
			ESX.ShowNotification('<font color=red>تصنيع السلاح متاح لمالك متجر الأسلحة والموظفين</font>')
		end
	end)
end

-- Create blips
Citizen.CreateThread(function()
	while not configready do Citizen.Wait(1000); end
	local pPSOS = Config.Zones.crafting.Pos
	local blip = AddBlipForCoord(pPSOS.x, pPSOS.y, pPSOS.z)

	SetBlipSprite (blip, 110)
	SetBlipDisplay(blip, 4)
	SetBlipScale  (blip, 1.0)
	SetBlipColour (blip, 1)
	SetBlipAsShortRange(blip, true)

	BeginTextCommandSetBlipName("STRING")
	AddTextComponentSubstringPlayerName('<FONT FACE="A9eelsh">ﺡﻼﺳ ﻊﻴﻨﺼﺗ')
	EndTextCommandSetBlipName(blip)
end)

function setblip_robbery(position)
	blipRobbery = AddBlipForCoord(position.x, position.y, position.z)
	SetBlipSprite(blipRobbery , 161)
	SetBlipScale(blipRobbery , 2.0)
	SetBlipColour(blipRobbery, 3)
	PulseBlip(blipRobbery)
end

RegisterNetEvent('esx_shops2:RobberyStartLeoJob')
AddEventHandler('esx_shops2:RobberyStartLeoJob', function(type, position)
	if type == 'start' then
		setblip_robbery(position)
	elseif type == 'stop' then
		RemoveBlip(blipRobbery)
	end
end)


RegisterNetEvent('esx_kr_shops:deletefoodtruck')
AddEventHandler('esx_kr_shops:deletefoodtruck', function()
	local foodTruck = GetClosestVehicle(GetEntityCoords(PlayerPedId()), 7.0, 1951180813, 70)
	ESX.Game.DeleteVehicle(foodTruck)
end)

press = false

RegisterNetEvent('hamada:esx_shops:pressToMark')
AddEventHandler('hamada:esx_shops:pressToMark', function(coords)
	local blipcoords = vector3(coords.x, coords.y, coords.z)
	local blip = AddBlipForCoord(blipcoords)
	SetBlipSprite(blip, 161)
	SetBlipColour(blip, 2)
	SetBlipCategory(blip, 7)
	BeginTextCommandSetBlipName('STRING')
	AddTextComponentSubstringPlayerName("Help!")
	EndTextCommandSetBlipName(blip)
	press = true
	Citizen.CreateThread(function()
		while press do
			Citizen.Wait(0)
			if IsControlJustReleased(0, 47) then
				SetNewWaypoint(coords.x, coords.y)
				ESX.ShowNotification('تم تحديد الموقع على <font color=red>الخريطة</font>')
				press = false
			end
		end
	end)
	Citizen.Wait(7000)
	press = false
	Wait(7 * 60 * 1000)
	RemoveBlip(blip)
end)
