body {
    margin-top: 100px;
    font-family: '<PERSON><PERSON>', sans-serif;
    overflow: hidden;
}

.toast {
    width: 100px;
    float: right;
}

.wrapper {
    width: 100px;
    height: auto;
    background-color: #1e1e1e;
    float: right;
}

.main {
    margin: 12px 16px 12px 56px;
    position: relative;
}

.main::before {
    font-size: 24px;
    top: calc(50% - 12px);
    left: -40px;
    line-height: 24px;
    position: absolute;
}

/* Success Notification*/

.success-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f058";
    color: #47cf73;
}

.success {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.success-border {
    border-left: 4px solid #47cf73;
}

/* Info Notification*/

.info-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f05a";
    color: #2f83ff;
}

.info {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.info-border {
    border-left: 4px solid #2f83ff;
}

/* Warning Notification */

.warning-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f071";
    color: #172CF5;
}

.warning {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.warning-border {
    border-left: 4px solid #172CF5;
}

/* Error Notification */


.error-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f06a";
    color: #dc3545;
}

.error {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.error-border {
    border-left: 4px solid #dc3545;
}

/* SMS Notification */

.phonemessage-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f7cd";
    color: #172CF5;
}

.phonemessage {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.phonemessage-border {
    border-left: 4px solid #172CF5;
}

/* Long Text Notification */

.neutral-icon::before {
    font-family: "Font Awesome 5 Free";
    content: "\f11c";
    color: #6c757d;
}

.neutral {
    background-color: rgba(20, 20, 20, 0.85);
    color: #fff;
    padding: 5px 5px 5px 5px;
}

.neutral-border {
    border-left: 4px solid #6c757d;
}