function Alert(title, message, time, type)
	SendNUIMessage({
		action = 'open',
		title = title,
		type = type,
		message = message,
		time = time,
	})
end

RegisterNetEvent('okokNotify:Alert')
AddEventHandler('okokNotify:Alert', function(title, message, time, type)
	Alert(title, message, time, type)
end)

-- Example Commands - Delete them

RegisterCommand('success', function()
	exports['okokNotify']:<PERSON><PERSON>("SUCCESS", "You have sent <span style='color:#47cf73'>420€</span> to <PERSON>!", 5000, 'success')
end)

RegisterCommand('info', function()
	exports['okokNotify']:Alert("INFO", "تم افتتاح الكازينو!", 5000, 'info')
end)

RegisterCommand('error', function()
	exports['okokNotify']:Al<PERSON>("ERROR", "Please try again later!", 5000, 'error')
end)

RegisterCommand('warning', function()
	exports['okokNotify']:Alert("WARNING", "You are getting nervous!", 5000, 'warning')
end)

RegisterCommand('phone', function()
	exports['okokNotify']:<PERSON><PERSON>("SMS", "<span style='color:#172CF5'>Tommy: </span> Where are you?", 5000, 'phonemessage')
end)

RegisterCommand('longtext', function()
	exports['okokNotify']:Alert("LONG MESSAGE", "الألم في حد ذاته هو الحب ، والعميل الرئيسي هو واحد من كثيرين.", 5000, 'neutral')
end)