@font-face {
  font-family: RB-Bold;
  src: url('fonts/RB-Bold.ttf');
}

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  height: 100%;
  width: 100%;
  direction: rtl;
  text-shadow:
       3px 3px 0 #000,
     -1px -1px 0 #000,  
      1px -1px 0 #000,
      -1px 1px 0 #000,
       1px 1px 0 #000;
}

.center-align {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

#wrapper {
  display: none;
  height: 50vw;
	width: 80vw;
	position: absolute;
  top: 50%;           
  overflow-y: auto;
  background-color: rgba(44, 44, 44, .9);
  left: 10%;
  transform: translate(0, -50%);
  box-shadow : 0px 0px 50px 0px #000;
}

#payment {
  display: none;
  width: 1600px;
  height: 50px;
  font-size: 14px;
  text-align: right;
  color: #fff;
  background-color: #000000;
	position: absolute;
  top: 940px;           
  overflow : auto;
  right: 160px;
}

#blocklol {
  width: 1600px;
  height: 20px;
  color: #fff;
  position: absolute;
  display: none;
  right: 5px;
  font-family: RB-Bold;
  font-weight: bold;
  top: 952.5px;
  font-size: 17.5px;
  right: 250px;
  margin-left: 5px;
  border: 1.5px solid #ffffff;
  padding: 5px 3px;
  border-radius: 5px;
}

.cartitem {
  width: auto;
  height: auto;
  position: relative;
  text-align: center;
  display: inline-block;
  top: 2.5%;
  right: 2.5%;
  margin-right: 15px;
  margin-top: 40px;
  border: 1.5px solid #757575;
  padding: 6px 3px;
}

#cart {
  display: none;
  padding: 5px 3px;
  border-radius: 5px;
  height: 35vw;
  width: 40vw;
  position: absolute;
  margin-top: 10%;
  left: 25%;                     
  overflow : auto;
  background-color: rgba(44, 44, 44, .9);
  
}

input[type=text], select {
  width: 150px;
  padding: 6px 10px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: RB-Bold;
}

h6 {
	font-size: 14px;
  color: #fff;
  font-family: RB-Bold;
  font-weight: bold;
}

h5 {
	font-size: 16px;
  color: #fff;
  font-family: RB-Bold;
  font-weight: bold;
  margin-bottom: -20px;
}

h4 {
	font-size: 18px;
  color: #fff;
  font-family: RB-Bold;
  font-weight: bold;
}

h3 {
	font-size: 20px;
  color: #fff;
  font-family: RB-Bold;
  font-weight: bold;
  margin-bottom: -20px;
}

h2 {
	font-size: 22px;
  border : 0;
  font-family: RB-Bold;
  font-weight: bold;
  border: 2px solid #ffffff;
  padding: 10px 6px;
  border-radius: 5px;
}

.h4 {
  color: #fff;
  margin-bottom: -20px;
}

.carticon {
  display: none;
  position: absolute;
  top: 35px;
  left: 15px;
}

.text-block {
  position: absolute;
  display: none;
  background-color: black;
  color: white;
  padding-left: 20px;
  padding-right: 20px;
  font-family: RB-Bold;
}


.image {
  width: 200px;
  height: 200px;
  position: relative;
  text-align: center;
  display: inline-block;
  left: 2.5%;
  margin-right: 100px;
  top: -175px;
  margin-top: 175px;
}


.textclass {
  font: 100 30px/1.3 'Arizonia', Helvetica, sans-serif;
  background-color: #181818;
  color: #ffffff;
  text-shadow: 4px 4px 0px rgba(0,0,0,0.1); 
}

#leftblock {
  display: none;
  height: 900px;
	width: 600px;
	position: absolute;
	top: 10%;
	right: 15%;
	border-radius: 15px;
	background-color: #181818;
}

.block {
  display: none;
  height: 50px;
  width: 100%;
  position: absolute;
  background-color: rgb(143, 143, 143);
}


/* width */
::-webkit-scrollbar {
  width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #181818; 
}
 

/* Handle */
::-webkit-scrollbar-thumb {
  background: rgb(255, 255, 255); 
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555; 
}

.button:hover {
  background-color: #ffffff;
  color: rgb(0, 0, 0);
}

.line { 
  height: 2px;
  background: rgb(255, 255, 255);
  width: 100%;
  background: webkit-gradient(linear, 0 0, 100% 0, from(#181818), to(#181818), color-stop(50%, white));
}



.button {
  border : 0;
  background: none;
  border: 2px solid #ffffff;
  border-radius: 15px;
  color: white;
  padding: 5px 10px;
  outline: none;
  text-align: center;
  text-decoration: none;
  display: block;
  font-size: 14px;
  margin: 4px 2px;
  font-family: RB-Bold;
}

.room-info-block {
  height: 100px;
  width: 100px;
  background-color: #555;
}
